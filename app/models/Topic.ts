// /src/app/api/models/Topic.js
import mongoose from 'mongoose';

const topicSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    unique: true, // Ensure titles are unique
  },
  title_en: {
    type: String,
    default: null, // Nullable for backward compatibility
  },
  title_ar: {
    type: String,
    default: null, // Nullable
  },
}, {
  timestamps: true // Add createdAt and updatedAt fields
});

const Topic = mongoose.models.Topic || mongoose.model('Topic', topicSchema);
export default Topic;