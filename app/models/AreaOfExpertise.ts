// /src/app/api/models/AreaOfExpertise.js
import mongoose from 'mongoose';

const areaOfExpertiseSchema = new mongoose.Schema({
  title_en: {
    type: String,
    required: true,
    unique: true, // Ensure titles are unique
  },
  title_ar: {
    type: String,
    default: null, // Nullable
  },
});
const AreaOfExpertise = mongoose.models.AreaOfExpertise || mongoose.model('AreaOfExpertise', areaOfExpertiseSchema);
export default AreaOfExpertise;