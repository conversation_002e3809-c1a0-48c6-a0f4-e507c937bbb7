import mongoose from "mongoose";

const mentorSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
  },
  password: { type: String, required: true },
  profileComplete: { type: Boolean, default: false },
  name_en: {
    type: String,
  },
  name_ar: {
    type: String,
  },
  gender: {
    type: String,
  },
  role: {
    type: String,
    enum: ["MENTOR", "ADMIN"],
    required: true,
  },
  profile_image: {
    path: {
      type: String,
    },
    url: {
      type: String,
    }
  },
  level: {
    type: String,
    required: false,
  },
  bio_en: {
    type: String,
  },
  bio_ar: {
    type: String,
  },
  english_session: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  linkedin_link: {
    type: String,
  },
  calendly_link: {
    type: String,
  },
  current_company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
  },
  skills: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Skill",
    },
  ],
  topics: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Topic",
    },
  ],
  areas_of_expertise: [
    {
      title_en: { type: String, required: true },
      title_ar: { type: String }
    }
  ],
  services: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Service",
    },
  ],

});

const Mentor = mongoose.models.Mentor || mongoose.model("Mentor", mentorSchema);
export default Mentor;
