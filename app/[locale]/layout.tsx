import "../globals.css";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import { notFound } from "next/navigation";
import { routing } from "@/i18n/routing";
import { LoadingProvider } from "./context/LoadingContext";
import { headers } from "next/headers";
import FontLoader from "./FontLoader";
import Footer from "@/components/Footer";
import Navbar from "@/components/Navbar";
import { Toaster } from "@/components/ui/toaster";
import { CookieProvider } from "@/components/context/CookieContext";
import { Metadata } from "next";
import React from "react";
import Script from "next/script";
import ClarityProvider from "@/components/ClarityProvider";

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: "en" | "ar" }>;
}

const inter = Inter({
  subsets: ["latin"],
  weight: ["200", "400", "500", "600", "700"],
  display: "swap",
  variable: "--font-inter",
});

const arabicFont = {
  fontFamily: "'Tajawal', sans-serif",
  link: "https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap",
};

export const metadata = {
  title: "Prohelpify",
  description: "Your guide to becoming a senior software developer , our experts will lead your learning journey",
  icons: {
    icon: "/assists/icons/P8Y.svg",
  },
  openGraph: {
    title: "Prohelpify",
    description: "Your guide to becoming a senior software developer , our experts will lead your learning journey",
    url: "https://prohelpify.com",
    siteName: "Prohelpify",
    images: [
      {
        url: "/assists/images/social-preview.jpg", // Updated to raster image
        width: 1200,
        height: 630,
        type: "image/jpeg",
      },
    ],
    locale: "en_US",
    type: "website",
  },
};



const styleDirection = (locale: string): "rtl" | "ltr" =>
  locale === "ar" ? "rtl" : "ltr";

const fontFamily = (locale: string): string =>
  locale === "ar" ? arabicFont.fontFamily : "var(--font-inter, sans-serif)";

export default async function RootLayout({ children, params }: RootLayoutProps) {
  const { locale } = await params;

  if (!routing.locales.includes(locale)) {
    notFound();
  }

  const isRTL = locale === "ar";

  const headersList = await headers();
  const pathname = headersList.get("x-pathname") || "";

  const messages = await getMessages();
  const canonicalUrl = `https://prohelpify.com/${locale}${pathname}`;

  return (
    <html lang={locale} dir={isRTL ? "rtl" : "ltr"}>
      <head>
        {locale === "ar" && (
          <link href={arabicFont.link} rel="preload" as="style" />
        )}
        <noscript>
          {locale === "ar" && <link href={arabicFont.link} rel="stylesheet" />}
        </noscript>
        <link rel="canonical" href={canonicalUrl} />
        {routing.locales.map((loc) => (
          <link
            key={loc}
            rel="alternate"
            hrefLang={loc}
            href={`https://www.example.com/${loc}${pathname}`}
          />
        ))}
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-EBGKJMLW9F"
        />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-EBGKJMLW9F');
            `,
          }}
        />
      </head>
      <body className="antialiased min-h-screen flex flex-col" style={{ fontFamily: fontFamily(locale) }}>
        {locale === "ar" && <FontLoader href={arabicFont.link} />}
        <NextIntlClientProvider messages={messages}>
          <LoadingProvider>
            <CookieProvider>
              <ClarityProvider>

                <Navbar />
                <main className="flex-grow">
                  {children}
                </main>
              </ClarityProvider>
              <Footer isRTL={isRTL} />
            </CookieProvider>
          </LoadingProvider>
          <Toaster />
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
