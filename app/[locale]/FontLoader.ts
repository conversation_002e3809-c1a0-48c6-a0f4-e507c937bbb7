"use client";

import { useEffect } from "react";

interface FontLoaderProp{
  href: string;
}
export default function FontLoader ({ href } :FontLoaderProp) {
  useEffect(() => {
    const link = document.createElement("link");
    link.href = href;
    link.rel = "stylesheet";
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, [href]);

  return null; 
}