"use client";

import { useLocale } from "next-intl";
import { useContext, CSSProperties } from "react";
import { LoadingContext } from "./context/LoadingContext";

import ContactUs from "@/components/ContactUs";
import Join from "@/components/Join";
import Landing from "@/components/Landing";
import Practices from "@/components/Practices";
import Team from "@/components/Team";
import FAQSection from "@/components/FAQSection";
import Companies from "@/components/Companies";
import Statistics from "@/components/Statistics";
import Technologies from "@/components/Technologies";
import { TestimonialsSection } from "@/components/TestimonialsSection";
import AboutUsSection from "@/components/AboutUsSection";
import Services from "@/components/Services";

interface LoadingContextType {
  isLoading: boolean;
}

export default function Home() {
  const locale = useLocale();
  const isRTL: boolean = locale === "ar";
  const { isLoading } = useContext(LoadingContext) as LoadingContextType;

  const spinnerStyle: CSSProperties = {
    width: "35px",
    height: "35px",
    borderRadius: "100%",
    borderWidth: "2px",
    borderStyle: "solid",
    borderColor: "rgb(54, 215, 183) rgb(54, 215, 183) transparent",
    display: "inline-block",
  };

  return (
    <main className="flex flex-col items-center w-[100%] relative justify-between gap-[6rem] pt-[4rem] sm:gap-[6rem] md:gap-[10rem] lg:gap-[12rem]">
      {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <span className="animate-spin" style={spinnerStyle}></span>
        </div>
      )}

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "Prohelpify - Your Guide to Becoming a Senior Programmer",
            description:
              "Learn coding, debugging, and best practices to advance your programming career with Prohelpify.",
            url: "https://www.example.com",
            image: "https://www.example.com/og-image.jpg",
          }),
        }}
      />

      <Landing isRTL={isRTL} />
      <AboutUsSection isRTL={isRTL} />
      <Services isRTL={isRTL} />
      <Statistics isRTL={isRTL} />
      <Companies isRTL={isRTL} />
      <Practices isRTL={isRTL} />
      <Team isRTL={isRTL} />
      <TestimonialsSection />
      <Technologies isRTL={isRTL} />
      <Join isRTL={isRTL} />
      <ContactUs isRTL={isRTL} />
      <FAQSection isRTL={isRTL} />
    </main>
  );
}
