"use client"

import React, { useState, useEffect, SetStateAction } from 'react';
import { Search, Filter, ChevronDown, X } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { MentorCard, MentorCardSkeleton } from '@/components/MentorCardFilter';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { Mentor , FilterOptions, Skill, AreaOfExpertise, Topic, Company } from "@/lib/type"

export default function MentorDirectory() {
  const [allMentors, setAllMentors] = useState<Mentor[]>([]);
  const [filteredMentors, setFilteredMentors] = useState<Mentor[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(3);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    techSkills: [],
    expertise: [],
    services: [],
    companies: [],
    topics: [],
    gender: [
      { _id: 'MALE', title: 'Male' },
      { _id: 'FEMALE', title: 'Female' }
    ],
    levels: ['Intern', 'Junior', 'Intermediate', 'Senior', 'Expert'],
    englishSessionOnly: false
  });
  const locale = useLocale();
  const isRTL = locale === 'ar'
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTechSkills, setSelectedTechSkills] = useState<string[]>([]);
  const [selectedExpertise, setSelectedExpertise] = useState<string[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [selectedGenders, setSelectedGenders] = useState<string[]>([]);
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [selectedSessionLanguages, setSelectedSessionLanguage] = useState<string[]>([]);
  const [englishSessionOnly, setEnglishSessionOnly] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  const t = useTranslations('mentors');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const extractMentors = (payload: any): Mentor[] => {
    const arr = payload?.data;
    if (!Array.isArray(arr)) {
      throw new Error('Unexpected API shape: expected an array at data.data');
    }
    return arr;
  };


useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const baseUrl = process.env.NEXT_PUBLIC_API_URL!;
        
        const mentorsRes = await fetch(`https://prohelpify.com/api/mentors`, { cache: 'no-store' });
        const mentorsJson = await mentorsRes.json();
        if (!mentorsJson.success) throw new Error(mentorsJson.message);
        const all = extractMentors(mentorsJson.data);
        setAllMentors(all);
        setFilteredMentors(all);

        const [skillsRes, servicesRes, expertiseRes, topicsRes, companiesRes] = await Promise.all([
          fetch(`${baseUrl}/api/skills`, { cache: 'no-store' }),
          fetch(`${baseUrl}/api/services`, { cache: 'no-store' }),
          fetch(`${baseUrl}/api/experties`, { cache: 'no-store' }),
          fetch(`${baseUrl}/api/topics`, { cache: 'no-store' }),
          fetch(`${baseUrl}/api/companies`, { cache: 'no-store' }),
        ]);
        const [skillsJson, servicesJson, expertiseJson, topicsJson, companiesJson] = await Promise.all([
          skillsRes.json(), servicesRes.json(), expertiseRes.json(), topicsRes.json(), companiesRes.json()
        ]);
        setFilterOptions({
          techSkills: skillsJson.data,
          expertise: expertiseJson.data,
          services: servicesJson.data,
          companies: companiesJson.data,
          topics: topicsJson.data,
          gender: [
            { _id: 'MALE', title: 'Male' },
            { _id: 'FEMALE', title: 'Female' }
          ],
          levels: ["JUNIOR", "MID", "SENIOR", "EXPERT"],
          englishSessionOnly: false
        });
      } catch (err: any) {
        console.error(err);
        setError(err.message || 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);


  useEffect(() => {
    const applyFilters = async () => {
      try {
        const filters: Record<string,string> = {};
        if (searchQuery) {
          filters.search = searchQuery;
          filters.searchFields = 'name_en,name_ar,skills.title,current_company.name';
        }
        if (selectedTechSkills.length) filters.skill = selectedTechSkills.join(',');
        if (selectedExpertise.length) filters.expertise = selectedExpertise.join(',');
        if (selectedServices.length) filters.service = selectedServices.join(',');
        if (selectedCompanies.length) filters.company = selectedCompanies.join(',');
        if (selectedTopics.length) filters.topic = selectedTopics.join(',');
        if (selectedGenders.length) {
          filters.gender = selectedGenders.map(g => g.toUpperCase()).join(',');
        }
        if (selectedLevels.length) filters.level = selectedLevels.join(',');
        if (englishSessionOnly) filters.english_only = 'true';

        filters.page = currentPage.toString();
        filters.limit = itemsPerPage.toString();

        const baseUrl = process.env.NEXT_PUBLIC_API_URL!;
        const qp = new URLSearchParams(filters);
        const resp = await fetch(`${baseUrl}/api/mentors?${qp}`, { cache:'no-store' });
        const json = await resp.json();
        if (!json.success) throw new Error(json.message);
        
        const mentorsData = json.data?.data || [];
        const totalItems = json.data?.total || 0; 
        
        setTotalPages(Math.ceil(totalItems / itemsPerPage) || 1);
        setFilteredMentors(mentorsData);
      } catch (err: any) {
        console.error(err);
        setError('Failed to apply filters');
      }
    };
    applyFilters();
  }, [
    allMentors,
    searchQuery,
    selectedTechSkills,
    selectedExpertise,
    selectedServices,
    selectedCompanies,
    selectedTopics,
    selectedGenders,
    selectedLevels,
    englishSessionOnly,
    currentPage,
    itemsPerPage
  ]);

useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-container')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

const handleDropdownToggle = (dropdown: string) => {
    if (openDropdown === dropdown) {
      setOpenDropdown(null);
    } else {
      setOpenDropdown(dropdown);
    }
  };

  const toggleFilter = (
    type: 'techSkills' | 'expertise' | 'services' | 'companies' | 'topics' | 'gender' | 'levels' | 'sessionLanguage',
    value: string,
    currentState: string[],
    setter: React.Dispatch<React.SetStateAction<string[]>>
  ) => {
    if (type === 'gender') {
      // For gender, we're now using the _id values ('male', 'female')
      if (currentState.includes(value)) {
        setter(currentState.filter(item => item !== value));
      } else {
        setter([...currentState, value]);
      }
    } else {
      if (currentState.includes(value)) {
        setter(currentState.filter(item => item !== value));
      } else {
        setter([...currentState, value]);
      }
    }
  };

const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedTechSkills([]);
    setSelectedExpertise([]);
    setSelectedServices([]);
    setSelectedCompanies([]);
    setSelectedTopics([]);
    setSelectedGenders([]);
    setSelectedLevels([]);
    setEnglishSessionOnly(false);
  };

const getActiveFiltersCount = () => {
    return (
      (searchQuery ? 1 : 0) +
      selectedTechSkills.length +
      selectedExpertise.length +
      selectedServices.length +
      selectedCompanies.length +
      selectedTopics.length +
      selectedGenders.length +
      selectedLevels.length +
      (englishSessionOnly ? 1 : 0)
    );
  };

  const FilterDropdown = ({ 
    title, 
    options, 
    selected, 
    dropdownId, 
    onToggle,
    isStringOption = false
  }: { 
    title: string; 
    options: { _id: string; title?: string; title_en?: string; name?: string }[] | string[]; 
    selected: string[]; 
    dropdownId: string; 
    onToggle: (value: string) => void;
    isStringOption?: boolean;
  }) => (
    <div className="relative dropdown-container ">
      <button
        onClick={() => handleDropdownToggle(dropdownId)}
        className="flex items-center gap-2 px-3 py-2 text-sm border rounded  bg-gray-600 hover:bg-transparent"
      >
        <span>{title}</span>
        {selected.length > 0 && (
          <span className="flex items-center justify-center h-5 w-5 text-xs bg-black text-white rounded-full">
            {selected.length}
          </span>
        )}
        <ChevronDown size={16} />
      </button>
      
      {openDropdown === dropdownId && (
        <div className={`absolute z-10 mt-1 w-56 bg-black text-white rounded-md shadow-lg p-2 border ${isRTL ? 'right-0' : 'left-0'}`}>
          <div className="max-h-60 overflow-y-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-600 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:hover:bg-gray-500">
            {options.map((option, index) => {
              const optionId = isStringOption ? `${option as string}-${index}` : (option as any)._id;
              const optionLabel = isStringOption 
                ? option as string 
                : (option as any).title || (option as any).title_en || (option as any).name;
              const isSelected = selected.includes(isStringOption ? option as string : optionId);
              const isDisabled = selected.length > 0 && !isSelected;

              return (
                <div 
                  key={optionId} 
                  className={`flex items-center gap-2 p-2 rounded ${
                    isDisabled 
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'hover:bg-gray-600 cursor-pointer'
                  }`}
                >
                  <div className="relative cursor-pointer">
                    <input
                      type="checkbox"
                      id={`${dropdownId}-${optionId}`}
                      checked={isSelected}
                      onChange={() => onToggle(isStringOption ? option as string : optionId)}
                      disabled={isDisabled}
                      className={`h-5 w-5 rounded-full border-2 mt-2 border-gray-300 appearance-none checked:border-main_color ${
                        isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'
                      }`}
                    />
                    {isSelected && (
                      <div className="absolute inset-0 my-auto flex items-center justify-center">
                        <svg
                          className="w-4 h-4 my-auto text-main_color"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={3}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                  <label 
                    htmlFor={`${dropdownId}-${optionId}`} 
                    className={`text-sm flex-1 ${
                      isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  >
                    {optionLabel}
                  </label>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );

  const translateGender = (gender: string): string => {
    // Convert API format (UPPERCASE) to display format
    const displayFormat = gender.charAt(0).toUpperCase() + gender.slice(1).toLowerCase();
    return t(displayFormat);
  };

const translateLevel = (level: string): string => {
    return t(level);
  };


  const filterConfigs = [
    {
      title: t('techSkills'),
      options: filterOptions.techSkills,
      selected: selectedTechSkills,
      dropdownId: 'tech-skills',
      type: 'techSkills',
      setter: setSelectedTechSkills,
      isStringOption: false
    },
    {
      title: t('expertise'),
      options: filterOptions.expertise,
      selected: selectedExpertise,
      dropdownId: 'expertise',
      type: 'expertise',
      setter: setSelectedExpertise,
      isStringOption: false
    },
    {
      title: t('services'),
      options: filterOptions.services,
      selected: selectedServices,
      dropdownId: 'services',
      type: 'services',
      setter: setSelectedServices,
      isStringOption: false
    },
    {
      title: t('companies'),
      options: filterOptions.companies,
      selected: selectedCompanies,
      dropdownId: 'companies',
      type: 'companies',
      setter: setSelectedCompanies,
      isStringOption: false
    },
    {
      title: t('topics'),
      options: filterOptions.topics,
      selected: selectedTopics,
      dropdownId: 'topics',
      type: 'topics',
      setter: setSelectedTopics,
      isStringOption: false
    },
    {
      title: t('gender'),
      options: filterOptions.gender,
      selected: selectedGenders,
      dropdownId: 'gender',
      type: 'gender',
      setter: setSelectedGenders,
      isStringOption: false
    },
    {
      title: t('level'),
      options: filterOptions.levels,
      selected: selectedLevels,
      dropdownId: 'levels',
      type: 'levels',
      setter: setSelectedLevels,
      isStringOption: true
    },
    {
      title: t('sessionLanguage'),
      options: [{ _id: 'english', title: 'English' }],
      selected: englishSessionOnly ? ['english'] : [],
      dropdownId: 'session-language',
      type: 'sessionLanguage',
      setter: (value: SetStateAction<string[]>) => {
        if (typeof value === 'function') {
          const newValue = value([]);
          setEnglishSessionOnly(newValue.includes('english'));
        } else {
          setEnglishSessionOnly(value.includes('english'));
        }
      },
      isStringOption: false
    }
  ];

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const pageParam = searchParams.get('page');
    if (pageParam) {
      const pageNumber = parseInt(pageParam);
      if (!isNaN(pageNumber) && pageNumber >= 1) {
        setCurrentPage(pageNumber);
      }
    }
  }, []);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set('page', page.toString());
      window.history.pushState({}, '', `${window.location.pathname}?${searchParams.toString()}`);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    if (startPage > 1) {
      pages.push(
        <PaginationItem key="first">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(1);
            }}
          >
            1
          </PaginationLink>
        </PaginationItem>
      );
      if (startPage > 2) {
        pages.push(
          <PaginationItem key="ellipsis-start">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink
            href="#"
            isActive={i === currentPage}
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(i);
            }}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <PaginationItem key="ellipsis-end">
            <PaginationEllipsis />
          </PaginationItem>
        );
      }
      pages.push(
        <PaginationItem key="last">
          <PaginationLink
            href="#"
            onClick={(e) => {
              e.preventDefault();
              handlePageChange(totalPages);
            }}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return (
      <div className="flex justify-center mt-8">
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(currentPage - 1);
                }}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                isRTL={isRTL}
                lang={locale as "ar" | "en"}
              >
                {t('previous')}
              </PaginationPrevious>
            </PaginationItem>
            {pages}
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(currentPage + 1);
                }}
                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                isRTL={isRTL}
                lang={locale as "ar" | "en"}
              >
                {t('next')}
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    );
  };

  return (
    <div dir={isRTL ? 'rtl' : 'ltr'} className="container mx-auto px-4 py-24">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{t('findMentor')}</h1>
      </div>
      
      <div className="mb-6">
        <div className="relative mb-4">
          <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
            <Search size={18} className="text-gray-500" />
          </div>
          <input
            type="text"
            placeholder={t('search')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={`w-full ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border rounded-lg bg-transparent focus:ring-2 focus:ring-green-500 focus:border-green-500`}
          />
        </div>
        
        <div className="flex flex-wrap items-center gap-2">
          <div className="flex items-center gap-2">
            <Filter size={18} className="text-gray-500" />
            <span className="text-sm font-medium">{t('filters')}</span>
          </div>
          
          {filterConfigs.map((config) => (
            <FilterDropdown
              key={config.dropdownId}
              title={config.title}
              options={config.options}
              selected={config.selected}
              dropdownId={config.dropdownId}
              onToggle={(value) => toggleFilter(
                config.type as 'techSkills' | 'expertise' | 'services' | 'companies' | 'topics' | 'gender' | 'levels' | 'sessionLanguage',
                value,
                config.selected,
                config.setter
              )}
              isStringOption={config.isStringOption}
            />
          ))}
          
          {getActiveFiltersCount() > 0 && (
            <button
              onClick={clearAllFilters}
              className="flex items-center gap-1 px-3 py-2 text-sm text-red-600 hover:bg-red-50 bg-red-100 rounded border border-red-200"
            >
              <X size={14} />
              <span>{t('clearAll')}</span>
            </button>
          )}
        </div>
      </div>
      
      <div className="mb-4 flex justify-between items-center">
        <p className="text-sm text-gray-600">
          {t('found')} <span className="font-medium">{filteredMentors.length}</span> {t('mentors')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {loading ? (
          [...Array(3)].map((_, index) => (
            <MentorCardSkeleton key={index} />
          ))
        ) : (
          filteredMentors.map((mentor) => (
            <MentorCard key={mentor._id} mentor={mentor} />
          ))
        )}
      </div>
      
      {!loading && filteredMentors.length > 0 && (
        <div className="flex justify-center">
          {renderPagination()}
        </div>
      )}
      
      {!loading && filteredMentors.length === 0 && (
        <div className="text-center py-16">
          <div className="mb-4">
            <Filter size={48} className="mx-auto text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">{t('noMentorsFound')}</h3>
          <p className="text-gray-500">{t('tryAdjusting')}</p>
        </div>
      )}
    </div>
  );
}