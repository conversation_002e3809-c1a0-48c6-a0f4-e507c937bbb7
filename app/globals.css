@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #0b1119;
}

body {
  /* position:-webkit-sticky ; */
  color: #ffffff;
  background: var(--background);
  font-family: Inter;
  overflow: hidden;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.container {
  /* margin:4%; */
  /* padding-inline: 5%; */
  padding-left: 3.5rem;
  padding-right: 3.5rem;
  /* position: absolute; */
}

.content {
  transition: opacity 0.5s ease-in-out;
}

.fade-in {
  opacity: 1;
}

.fade-out {
  opacity: 0;
}

@layer components {
  .main-about_us {
    @apply w-full h-max container max-sm:p-3 flex flex-col justify-around gap-12;
  }

  .why-about_us {
    @apply font-bold text-2xl text-text_color mb-3 max-md:text-xl max-md:text-center;
  }

  .about_p {
    @apply font-bold text-xl text-text_color max-md:text-lg max-md:text-center;
  }

  .contact-main {
    @apply relative container max-md:p-0 w-full h-full;
  }

  .contact-h2 {
    @apply text-4xl max-md:text-center max-md:text-3xl max-md:w-full max-lg:w-10/12 w-7/12 font-bold text-main_color mb-4 max-md:mx-auto;
  }

  .contact-h3 {
    @apply text-[2rem] max-md:text-center max-md:text-2xl w-fit font-bold text-text_color mb-2 max-md:mx-auto;
  }

  .contact-container {
    @apply flex max-md:flex-col-reverse justify-center mx-auto items-center gap-6 h-max max-md:px-0 mt-12;
  }

  .footer-container {
    @apply grid grid-cols-8 max-md:grid-cols-1 max-md:gap-y-10 items-center justify-center mx-auto px-12 py-8 bg-[#080809];
  }

  .join-container {
    @apply flex flex-col-reverse md:flex-row px-5 lg:px-0 justify-between mx-auto items-center gap-20;
  }

  .landing-container {
    @apply container relative w-[100%] h-max mt-[40px] flex max-lg:flex-col max-md:w-full max-md:px-[2rem] justify-between gap-7 mx-auto;
  }

  .landing-desc {
    @apply text-text_color max-md:font-light font-normal max-md:text-[1rem] w-5/6 text-sm text-[1.3rem] max-md:mx-auto my-8;
  }

  .nav-ul {
    @apply relative z-50 flex flex-col md:flex-row max-md:text-sm w-fit overflow-hidden gap-10 text-white items-center
  }

  .nav-select {
    @apply px-3 py-1.5 bg-[#0B1119] text-white rounded-lg cursor-pointer hover:bg-opacity-90 transition-colors appearance-none border text-center shadow-white
  }

  .practis-item {
    @apply flex max-sm:flex-col lg:px-0 max-sm:mx-auto gap-7 p-4 w-full h-full bg-[#1D2B3B] rounded-2xl
  }

  .team-togggle {
    @apply px-6 py-2 text-sm font-semibold text-white bg-main_color rounded-lg hover:bg-opacity-90
  }

  .filters-container {
    @apply p-[4rem] mt-[5rem] flex flex-col gap-20
  }
}

@layer base {
  button {
    @apply w-fit px-8 py-2 max-md:mx-auto rounded-lg bg-main_color font-bold text-[1rem] text-text_color hover:cursor-pointer;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@keyframes infinite-scroll {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-33.333%);
  }
}

@keyframes infinite-scroll-rtl {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(33.333%);
  }
}

.animate-infinite-scroll {
  animation: infinite-scroll 45s linear infinite;
}

.animate-infinite-scroll-rtl {
  animation: infinite-scroll-rtl 45s linear infinite;
}