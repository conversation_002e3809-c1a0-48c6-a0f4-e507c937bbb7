// import { NextRequest, NextResponse } from "next/server";
// import bcrypt from "bcryptjs";
// import { generateToken } from "../../lib/jwt";
// import Mentor from "../../../models/Mentor";
// import { connectDB } from "../../lib/db";
// import validator from "validator";

// interface RegisterBody {
//   email: string;
//   password: string;
//   role: "MENTOR" | "ADMIN";
// }

// export const dynamic = "force-dynamic";

// // const setCorsHeaders = (response: NextResponse) => {

// // };

// export async function POST(req: NextRequest) {
//   await connectDB();

//   try {
//     // Parse FormData instead of JSON
//     const formData = await req.formData();
//     const email = formData.get("email");
//     const password = formData.get("password");
//     const role = formData.get("role");
//     // Input validation
//     if (!email || !password) {
//       return NextResponse.json(
//         { error: "Email and password are required" },
//         {
//           status: 400,
//           headers: {
//             "Content-Type": "application/json",
//             "Access-Control-Allow-Origin": "*",
//             "Access-Control-Allow-Methods": "POST, OPTIONS",
//             "Access-Control-Allow-Headers": "Content-Type",
//           },
//         }
//       );
//     }

//     if (!validator.isEmail(String(email))) {
//       return NextResponse.json(
//         { error: "Invalid email format" },
//         {
//           status: 400,
//           headers: {
//             "Content-Type": "application/json",
//             "Access-Control-Allow-Origin": "*",
//             "Access-Control-Allow-Methods": "POST, OPTIONS",
//             "Access-Control-Allow-Headers": "Content-Type",
//           },
//         }
//       );
//     }

//     if (String(password).length < 8) {
//       return NextResponse.json(
//         { error: "Password must be at least 8 characters" },
//         {
//           status: 400,
//           headers: {
//             "Content-Type": "application/json",
//             "Access-Control-Allow-Origin": "*",
//             "Access-Control-Allow-Methods": "POST, OPTIONS",
//             "Access-Control-Allow-Headers": "Content-Type",
//           },
//         }
//       );
//     }

//     // Check if mentor exists
//     const existingMentor = await Mentor.findOne({ email });
//     if (existingMentor) {
//       return NextResponse.json(
//         { error: "Email already registered" },
//         {
//           status: 400,
//           headers: {
//             "Content-Type": "application/json",
//             "Access-Control-Allow-Origin": "*",
//             "Access-Control-Allow-Methods": "POST, OPTIONS",
//             "Access-Control-Allow-Headers": "Content-Type",
//           },
//         }
//       );
//     }

//     // Hash password
//     const salt = await bcrypt.genSalt(10);
//     const hashedPassword = await bcrypt.hash(String(password), salt);

//     // Create mentor
//     const mentor = await Mentor.create({
//       email,
//       password: hashedPassword,
//       role: role,
//       profileComplete: false,
//     });
//     console.log(mentor);

//     // Generate JWT
//     const token = generateToken({ userId: mentor._id.toString() });

//     return NextResponse.json(
//       {
//         success: true,
//         message: "Mentor registered successfully",
//         statusCode: 201,
//         data: {
//           token,
//           mentor: {
//             _id: mentor._id,
//             email: mentor.email,
//             role: mentor.role,
//             profileComplete: mentor.profileComplete,
//           },
//         },
//       },
//       {
//         status: 201,
//         headers: {
//           "Content-Type": "application/json",
//           "Access-Control-Allow-Origin": "*",
//           "Access-Control-Allow-Methods": "POST, OPTIONS",
//           "Access-Control-Allow-Headers": "Content-Type",
//         },
//       }
//     );
//   } catch (error) {
//     console.error("Registration error:", error);
//     return NextResponse.json(
//       {
//         error: "Registration failed",
//         details: error instanceof Error ? error.message : "Unknown error",
//       },
//       {
//         status: 500,
//         headers: {
//           "Content-Type": "application/json",
//           "Access-Control-Allow-Origin": "*",
//           "Access-Control-Allow-Methods": "POST, OPTIONS",
//           "Access-Control-Allow-Headers": "Content-Type",
//         },
//       }
//     );
//   }
// }

// export async function OPTIONS() {
//   return new Response(null, {
//     status: 204,
//     headers: {
//       "Access-Control-Allow-Origin": "*",
//       "Access-Control-Allow-Methods": "POST, OPTIONS",
//       "Access-Control-Allow-Headers": "Content-Type, Authorization",
//     },
//   });
// }



import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { generateToken } from "../../lib/jwt";
import Mentor from "../../../models/Mentor";
import { connectDB } from "../../lib/db";
import validator from "validator";

export const dynamic = "force-dynamic";

export async function POST(req: NextRequest) {
  await connectDB();

  try {
    // Parse FormData
    const formData = await req.formData();
    const email = formData.get("email");
    const password = formData.get("password");
    const role = formData.get("role");

    // Input validation
    if (!email || !password || !role) {
      return NextResponse.json(
        { success: false, error: "Email, password, and role are required" },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        }
      );
    }

    if (!validator.isEmail(String(email))) {
      return NextResponse.json(
        { success: false, error: "Invalid email format" },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        }
      );
    }

    if (String(password).length < 8) {
      return NextResponse.json(
        { success: false, error: "Password must be at least 8 characters" },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        }
      );
    }

    if (!["MENTOR", "ADMIN"].includes(String(role))) {
      return NextResponse.json(
        { success: false, error: "Role must be MENTOR or ADMIN" },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        }
      );
    }

    // Check if mentor exists
    const existingMentor = await Mentor.findOne({ email });
    if (existingMentor) {
      return NextResponse.json(
        { success: false, error: "Email already registered" },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
          },
        }
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(String(password), salt);

    // Create mentor
    const mentor = await Mentor.create({
      email,
      password: hashedPassword,
      role,
      profileComplete: false,
    });

    // Generate JWT with role
    const token = generateToken({ userId: mentor._id.toString(), role });

    return NextResponse.json(
      {
        success: true,
        message: "Mentor registered successfully",
        statusCode: 201,
        data: {
          token,
          mentor: {
            _id: mentor._id,
            email: mentor.email,
            role: mentor.role,
            profileComplete: mentor.profileComplete,
          },
        },
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Registration failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}