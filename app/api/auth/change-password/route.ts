import { NextResponse } from "next/server";
import { ApiError } from "../../utils/ApiError";
import { errorHandler } from "../../utils/errorHandler";
import <PERSON><PERSON> from "../../../models/Mentor";
import { connectDB } from "../../lib/db";
import bcrypt from "bcryptjs";
import { verifyToken } from "../../lib/jwt";



export async function POST(request: Request) {
  await connectDB();

  try {
    const { currentPassword, newPassword, confirmPassword } = await request.json();

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      throw new ApiError(400, "All password fields are required");
    }

    if (newPassword.length < 8) {
      throw new ApiError(400, "New password must be at least 8 characters");
    }

    if (newPassword !== confirmPassword) {
      throw new ApiError(400, "New passwords don't match");
    }

    if (currentPassword === newPassword) {
      throw new ApiError(400, "New password must be different from current password");
    }

    // Get token from Authorization header
    const token = request.headers.get('Authorization')?.split(' ')[1];
    if (!token) {
      throw new ApiError(401, "Authorization token required");
    }

    // Verify token and get user ID
    const decoded = verifyToken(token);
    if (!decoded || !decoded.userId) {
      throw new ApiError(401, "Invalid or expired token");
    }

    // Find mentor with password
    const mentor = await Mentor.findById(decoded.userId).select('+password');
    if (!mentor) {
      throw new ApiError(404, "User not found");
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, mentor.password);
    if (!isPasswordValid) {
      throw new ApiError(401, "Current password is incorrect");
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    mentor.password = hashedPassword;
    await mentor.save();

    return NextResponse.json(
      { 
        success: true, 
        message: "Password updated successfully",
        profileComplete: mentor.profileComplete // Include profile status
      },
      { status: 200 }
    );

  } catch (error) {
    return errorHandler(error);
  }
}
