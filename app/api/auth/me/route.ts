// app/api/auth/me/route.ts
import { NextRequest, NextResponse } from "next/server";
import { ApiError } from "../../utils/ApiError";
import { errorHandler } from "../../utils/errorHandler";
import { connectDB } from "../../lib/db";
import <PERSON><PERSON> from "../../../models/Mentor";
import { verifyToken } from "../../lib/jwt";
import { Types } from "mongoose";
import { MentorType } from "../../utils/types";
export interface BaseResponse<T> {
  message?: string;
  success?: boolean;
  data: T;
  statusCode?: number;
}

export async function GET(req: NextRequest) {
  await connectDB();

  try {
    const token = req.headers.get("Authorization")?.replace("Bearer ", "");
    if (!token) throw new ApiError(401, "No token provided");

    const decoded = verifyToken(token); // Implement this based on your JWT setup
    const mentor = await Mentor.findById(decoded.id)
      .populate([
        "current_company",
        "skills",
        "topics",
        "areas_of_expertise",
        "services",
      ])
      .select("-password -__v");

    if (!mentor) throw new ApiError(404, "Mentor not found");

    const response: BaseResponse<MentorType> = {
      success: true,
      message: "Mentor retrieved successfully",
      statusCode: 200,
      data: mentor,
    };

    return NextResponse.json(response, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    }
  );
}
