import { NextResponse } from "next/server";
import { ApiError } from "../../utils/ApiError";
import { errorHandler } from "../../utils/errorHandler";
import <PERSON><PERSON> from "../../../models/Mentor";
import { connectDB } from "../../lib/db";
import bcrypt from "bcryptjs"
import { generateToken } from "../../lib/jwt";

export async function POST(request: Request) {
  await connectDB();

  try {
    const { email, password } = await request.json();

    // Validation
    if (!email || !password) {
      throw new ApiError(400, "Email and password are required");
    }

    // Find mentor
    const mentor = await Mentor.findOne({ email }).select("+password");
    if (!mentor) {
      throw new ApiError(401, "Invalid email or password");
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, mentor.password);
    if (!isPasswordValid) {
      throw new ApiError(401, "Invalid email or password");
    }

    // Generate token
    const token = generateToken({ userId: mentor._id.toString() });

    // Remove password from response
    const mentorWithoutPassword = mentor.toObject();
    delete mentorWithoutPassword.password;

    return NextResponse.json(
      { success: true, token, mentor: mentorWithoutPassword },
      {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}
export async function OPTIONS() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
