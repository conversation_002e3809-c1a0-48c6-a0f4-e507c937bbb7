import jwt from 'jsonwebtoken';
import { env } from 'process';

// Define token payload type
interface TokenPayload {
  userId: string;
  [key: string]: any; // Additional claims
}

// Generate token
export const generateToken = (payload: TokenPayload): string => {
  if (!env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }
  
  return jwt.sign(payload, env.JWT_SECRET, {
    expiresIn: '30d', // Token expires in 30 days
  });
};

// Verify token
export const verifyToken = (token: string): TokenPayload => {
  if (!env.JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }
  return jwt.verify(token, env.JWT_SECRET) as TokenPayload;
};