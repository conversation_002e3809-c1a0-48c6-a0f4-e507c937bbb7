import mongoose from "mongoose";
import { ApiError } from "next/dist/server/api-utils";
import { NextResponse } from "next/server";
import { connectDB } from "../../lib/db";
import { errorHandler } from "../../utils/errorHandler";
import Skill from "@/app/models/Skill";
export async function DELETE(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Skill ID is required");
    }

    // Find and delete the skill
    const deletedSkill = await Skill.findByIdAndDelete(id);
    if (!deletedSkill) {
      throw new ApiError(404, "Skill not found");
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Skill deleted successfully",
        data: deletedSkill,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}


export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;
    const body = await request.json();
    const { title } = body;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Skill ID is required");
    }

    // Validate input
    if (!title) {
      throw new ApiError(400, "Title is required");
    }

    // Check if skill exists
    const skill = await Skill.findById(id);
    if (!skill) {
      throw new ApiError(404, "Skill not found");
    }

    // Check for duplicate title (case-insensitive, excluding current skill)
    const existingSkill = await Skill.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
      _id: { $ne: id },
    });
    if (existingSkill) {
      throw new ApiError(400, `Skill with title "${title}" already exists`);
    }

    // Update skill
    skill.title = title;
    const updatedSkill = await skill.save();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Skill updated successfully",
        data: updatedSkill,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}

