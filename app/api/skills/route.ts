// /src/app/api/skills/route.ts
import { NextResponse } from 'next/server';
import Skill from '../../models/Skill';
import { connectDB } from '../lib/db'
import { errorHandler } from '../utils/errorHandler';
import { ApiError } from '../utils/ApiError';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name') || '';
    const paginationParam = searchParams.get('pagination') || 'false';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const usePagination = paginationParam.toLowerCase() === 'true';

    // Base query with search
    let query = Skill.find().sort({'_id': -1});
    
    if (name) {
      query = query.where('title').regex(new RegExp(name, 'i'));
    }

    query = query.select('_id title').lean();

    let data;

    if (usePagination) {
      // Get total count for pagination info
      const total = await Skill.countDocuments(query.getFilter());
      
      // Apply pagination
      const skills = await query.skip((page - 1) * limit).limit(limit).exec();

      data = {data: skills, total};
    } else {
      // Return all results without pagination
      data = await query.exec();
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Skills retrieved successfully'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
      }
    });

  } catch (error: any) {
    console.error('Error fetching skills:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve skills',
      error: error.message
    }, {
      status: 500
    });
  }
}


export async function POST(request: Request) {
  await connectDB();

  try {
    const body = await request.json();
    const { title } = body;

    // Validate required fields
    if (!title) {
      throw new ApiError(400, "Title is required");
    }

    // Check for duplicate title (case-insensitive)
    const existingSkill = await Skill.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
    });
    if (existingSkill) {
      throw new ApiError(400, `Skill with title "${title}" already exists`);
    }

    // Create new skill
    const newSkill = await Skill.create({ title });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Skill created successfully",
        data: newSkill,
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}



export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}

