// /src/app/api/utils/errorHandler.ts
import { NextResponse } from 'next/server';
import { ApiError } from './ApiError';

export function errorHandler(error: unknown) {
  // Default error response
  let statusCode = 500;
  let message = 'An unexpected error occurred';

  // Handle ApiError instances
  if (error instanceof ApiError) {
    statusCode = error.statusCode;
    message = error.message;
  }
  // Handle other Error instances
  else if (error instanceof Error) {
    message = error.message;
  }
  // Handle non-Error objects (e.g., thrown strings)
  else if (typeof error === 'string') {
    message = error;
  }

  // Log the error for debugging
  console.error('Error:', error);

  // Return a JSON response
  return NextResponse.json({ error: message }, { status: statusCode });
}