// /src/app/api/utils/ApiError.ts
export class ApiError extends Error {
    statusCode: number;
  
    constructor(statusCode: number, message: string) {
      super(message);
      this.statusCode = statusCode;
      this.name = this.constructor.name;
  
      // Ensure the stack trace is captured (useful for debugging)
      if (Error.captureStackTrace) {
        Error.captureStackTrace(this, this.constructor);
      }
    }
  }