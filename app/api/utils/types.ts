import { Types } from "mongoose";

export interface MentorType {
  _id: string;
  email: string;
  name: string;
  bio: string;
  gender?: string;
  profile_image?: string;
  level?: string;
  english_session?: boolean;
  linkedin_link?: string;
  calendly_link?: string;
  current_company?: {
    _id: string;
    name: string;
  };
  skills?: Array<{
    _id: string;
    title: string;
  }>;
  topics?: Array<{
    _id: string;
    title: string;
  }>;
  areas_of_expertise?: Array<{
    title_en: string;
    title_ar?: string | null;
  }>;
  services?: Array<{
    _id: string;
    title: string;
  }>;
  createdAt?: Date;
  updatedAt?: Date;
}