// app/api/mentors/[id]/route.ts
import { NextResponse } from "next/server";
import { ApiError } from "../../utils/ApiError";
import { errorHandler } from "../../utils/errorHandler";
import { connectDB } from "../../lib/db";
import Mentor from "../../../models/Mentor";
import { MentorType } from "../../utils/types";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import { writeFile, mkdir } from "fs/promises";
import Skill from "@/app/models/Skill";
import Topic from "@/app/models/Topic";
import AreaOfExpertise from "@/app/models/AreaOfExpertise";
import Service from "@/app/models/Service";
import Company from "@/app/models/Company";
import mongoose from "mongoose";
import { verifyToken } from "../../lib/jwt";
import cloudinary from "../../lib/cloudinary";

export interface BaseResponse<T> {
  message?: string;
  success?: boolean;
  data: T;
  statusCode?: number;
}

export async function GET(request: Request, { params }: any) {
  await connectDB();

  try {
    const { id } = params;

    if (!id) {
      throw new ApiError(400, "Mentor ID is required");
    }

    const populateFields = [
      "current_company",
      "skills",
      "topics",
      "areas_of_expertise",
      "services",
    ];

    const mentor = await Mentor.findById(id)
      .populate(populateFields)
      .select("-password -__v");

    if (!mentor) {
      throw new ApiError(404, "Mentor not found");
    }

    const response: BaseResponse<MentorType> = {
      success: true,
      message: "Mentor retrieved successfully",
      statusCode: 200,
      data: mentor,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET,PATCH, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    // 1. Verify Authentication
    const token = request.headers.get("Authorization")?.split(" ")[1];
    if (!token) throw new ApiError(401, "Unauthorized");

    // 2. Parse Form Data
    const formData = await request.formData();
    const { id } = params;

    // 3. Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Invalid Mentor ID");
    }

    // 4. Extract and Validate Required Fields
    const name_en = formData.get("name_en") as string;
    if (!name_en) throw new ApiError(400, "English name is required");

    // 5. Extract Optional Fields
    const name_ar = formData.get("name_ar") as string;
    const gender = formData.get("gender") as string;
    const bio_en = formData.get("bio_en") as string;
    const bio_ar = formData.get("bio_ar") as string;
    const english_session = formData.get("english_session") === "true";
    const linkedin_link = formData.get("linkedin_link") as string;
    const calendly_link = formData.get("calendly_link") as string;
    const current_company = formData.get("current_company") as string;
    const level = formData.get("level") as string;

    // 6. Parse Array Fields
    const getArrayFromFormData = (fd: any, keyPrefix: string): string[] => {
      const values: string[] = [];
      for (const [key, value] of fd.entries()) {
        if (key.startsWith(`${keyPrefix}[`)) {
          values.push(value as string);
        }
      }
      return values;
    };

    const skills = getArrayFromFormData(formData, "skills");
    const topics = getArrayFromFormData(formData, "topics");
    const services = getArrayFromFormData(formData, "services");

    // 7. Parse Embedded Arrays (areas_of_expertise)
    // Try different ways to get areas_of_expertise from form data
    let areas_of_expertise_raw: any = null;

    // First, try to get it as a direct field
    const directField = formData.get("areas_of_expertise");
    if (directField) {
      areas_of_expertise_raw = directField;
      console.log("Found areas_of_expertise as direct field:", areas_of_expertise_raw);
    }

    // If not found, try to get it as an array field
    if (!areas_of_expertise_raw) {
      const arrayField = getArrayFromFormData(formData, "areas_of_expertise");
      if (arrayField && arrayField.length > 0) {
        // If we have multiple JSON strings, we need to parse each one individually
        if (arrayField.length > 1) {
          try {
            const parsedArray = arrayField.map(item => JSON.parse(item as string));
            areas_of_expertise_raw = JSON.stringify(parsedArray);
            console.log("Found and parsed multiple areas_of_expertise items:", areas_of_expertise_raw);
          } catch (parseError) {
            console.error("Error parsing individual areas_of_expertise items:", parseError);
            areas_of_expertise_raw = arrayField;
          }
        } else {
          areas_of_expertise_raw = arrayField[0];
        }
        console.log("Found areas_of_expertise as array field:", areas_of_expertise_raw);
      }
    }

    // Check for the specific format you're using in Postman
    try {
      for (const [key, value] of (formData as any).entries()) {
        console.log(`Form data key: ${key}, value: ${value}`);
        if (key === 'areas_of_expertise[]') {
          areas_of_expertise_raw = value;
          console.log("Found areas_of_expertise[] field:", areas_of_expertise_raw);
          break;
        }
      }
    } catch (error) {
      console.error("Error iterating form data:", error);
    }

    // Default to empty array
    let areas_of_expertise: { title_en: string; title_ar: string | null }[] = [];

    // Add sample data for testing if needed (set to false by default)
    const addSampleData = false;

    try {
      // If it's not provided or empty, use an empty array
      if (!areas_of_expertise_raw || areas_of_expertise_raw === '[]' ||
        (typeof areas_of_expertise_raw === 'string' && areas_of_expertise_raw.trim() === '')) {
        console.log("Using empty array for areas_of_expertise");
        areas_of_expertise = [];

        // Add sample data for testing if enabled
        if (addSampleData) {
          areas_of_expertise = [
            {
              title_en: "Backend Development",
              title_ar: "تطوير الواجهة الخلفية"
            },
            {
              title_en: "DevOps",
              title_ar: "عمليات التطوير"
            }
          ];
          console.log("Added sample areas_of_expertise for testing");
        }
      } else {
        // Parse the JSON string
        const parsedData = JSON.parse(areas_of_expertise_raw);
        console.log("Parsed areas_of_expertise:", parsedData);

        // Handle if a single object is provided instead of an array
        if (!Array.isArray(parsedData) && typeof parsedData === 'object' && parsedData !== null) {
          console.log("Converting single object to array:", parsedData);
          // Convert single object to array with one item
          const singleItem = parsedData;
          if (singleItem.title_en) {
            areas_of_expertise = [{
              title_en: singleItem.title_en,
              title_ar: singleItem.title_ar || null
            }];
            console.log("Converted to array:", areas_of_expertise);
          } else {
            throw new ApiError(400, "areas_of_expertise object must have a title_en property");
          }
        } else if (!Array.isArray(parsedData)) {
          throw new ApiError(400, "areas_of_expertise must be an array or a valid object");
        } else {
          // Only process the array if we haven't already set areas_of_expertise
          // Process each item in the array
          areas_of_expertise = parsedData.map((item: any) => {
            // Handle if item is already in the correct format
            if (typeof item === 'object' && item !== null) {
              if (!item.title_en) {
                throw new ApiError(400, "title_en is required for each area of expertise");
              }
              return {
                title_en: item.title_en,
                title_ar: item.title_ar || null
              };
            }
            // Handle if item is a string
            else if (typeof item === 'string') {
              return {
                title_en: item,
                title_ar: null
              };
            } else {
              throw new ApiError(400, "Each area of expertise must be a string or have a title_en property");
            }
          });
        }
      }

      console.log("Final areas_of_expertise:", areas_of_expertise);
    } catch (error) {
      console.error("Error parsing areas_of_expertise:", error);
      throw new ApiError(400, "Invalid areas_of_expertise format: " + (error instanceof Error ? error.message : String(error)));
    }

    // 8. Handle Profile Image Upload with Cloudinary
    let profile_image = null;
    const profile_image_file = formData.get("profile_image") as File | null;

    if (profile_image_file) {
      if (!["image/jpeg", "image/png", "image/webp"].includes(profile_image_file.type)) {
        throw new ApiError(400, "Only JPEG, PNG, and WebP images are allowed");
      }

      try {
        // Convert file to base64 for Cloudinary upload
        const buffer = Buffer.from(await profile_image_file.arrayBuffer());
        const base64Image = `data:${profile_image_file.type};base64,${buffer.toString('base64')}`;

        // Upload to Cloudinary
        const uploadResult = await cloudinary.uploader.upload(base64Image, {
          folder: 'mentor-profiles',
          resource_type: 'image',
          transformation: [
            { width: 500, height: 500, crop: 'limit' },
            { quality: 'auto:good' }
          ]
        });

        profile_image = {
          path: uploadResult.public_id,
          url: uploadResult.secure_url,
        };
      } catch (error) {
        console.error('Cloudinary upload error:', error);
        throw new ApiError(500, "Failed to upload image to Cloudinary");
      }
    }

    // 9. Process Entities (Skills, Topics, Company, Services)
    const processEntities = async (
      model: any,
      items: string[],
      isAreaOfExpertiseOrService: boolean = false
    ) => {
      return Promise.all(
        items.map(async (item) => {
          if (!item || typeof item !== "string") {
            throw new ApiError(400, `Invalid ${model.modelName} input: ${item}`);
          }
          // Check if item is a valid ObjectId
          if (mongoose.Types.ObjectId.isValid(item)) {
            const doc = await model.findById(item);
            if (!doc) throw new ApiError(400, `Invalid ${model.modelName} ID: ${item}`);
            return doc._id;
          } else {
            // Item is a name, check if it exists (case-insensitive)
            const query = isAreaOfExpertiseOrService
              ? { title_en: { $regex: `^${item}$`, $options: "i" } }
              : { [model.modelName === "Company" ? "name" : "title"]: { $regex: `^${item}$`, $options: "i" } };
            let doc = await model.findOne(query);

            if (!doc) {
              // Create new entity
              const newEntity = isAreaOfExpertiseOrService
                ? { title_en: item, title_ar: null }
                : { [model.modelName === "Company" ? "name" : "title"]: item };
              doc = await model.create(newEntity);
            }
            return doc._id;
          }
        })
      );
    };

    const processCompany = async (companyInput: string | null) => {
      if (!companyInput) return null;
      // Check if input is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(companyInput)) {
        const company = await Company.findById(companyInput);
        if (!company) throw new ApiError(400, `Invalid Company ID: ${companyInput}`);
        return company._id;
      } else {
        // Input is a name, check if it exists (case-insensitive)
        let company = await Company.findOne({ name: { $regex: `^${companyInput}$`, $options: "i" } });
        if (!company) {
          // Create new company
          company = await Company.create({ name: companyInput });
        }
        return company._id;
      }
    };

    // Process skills, topics, company, and services
    const [skillIds, topicIds, companyId, serviceIds] = await Promise.all([
      processEntities(Skill, skills),
      processEntities(Topic, topics),
      processCompany(current_company),
      processEntities(Service, services, true),
    ]);

    // 10. Update Mentor Profile
    const updateData: any = {
      name_en,
      name_ar,
      gender,
      level,
      bio_en,
      bio_ar,
      english_session,
      linkedin_link,
      calendly_link,
      current_company: companyId,
      skills: skillIds,
      topics: topicIds,
      areas_of_expertise,
      services: serviceIds,
      profileComplete: true,
    };

    console.log("Update data areas_of_expertise:", updateData.areas_of_expertise);

    // Ensure areas_of_expertise is in the correct format
    if (Array.isArray(updateData.areas_of_expertise)) {
      // Make sure each item has the required structure
      updateData.areas_of_expertise = updateData.areas_of_expertise.map((item: any) => {
        if (typeof item === 'string') {
          return { title_en: item, title_ar: null };
        } else if (typeof item === 'object' && item !== null) {
          return {
            title_en: item.title_en || 'Unknown',
            title_ar: item.title_ar || null
          };
        } else {
          return { title_en: 'Unknown', title_ar: null };
        }
      });
    } else if (!updateData.areas_of_expertise) {
      // If it's null or undefined, set it to an empty array
      updateData.areas_of_expertise = [];
    }

    console.log("Final update data areas_of_expertise:", updateData.areas_of_expertise);

    if (profile_image) {
      updateData.profile_image = profile_image;
    } else {
      const mentor = await Mentor.findById(id);
      if (mentor?.profile_image) {
        updateData.profile_image = mentor.profile_image;
      }
    }

    const updatedMentor = await Mentor.findByIdAndUpdate(id, updateData, { new: true }).select("-password");

    if (!updatedMentor) throw new ApiError(404, "Mentor not found");

    console.log("Updated mentor:", JSON.stringify(updatedMentor, null, 2));
    console.log("Updated mentor areas_of_expertise:", updatedMentor.areas_of_expertise);

    // 11. Return Success Response
    return NextResponse.json(
      {
        success: true,
        data: { mentor: updatedMentor },
        statusCode: 200
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "PATCH, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
// export async function OPTIONS() {
//   return new Response(null, {
//     status: 204,
//     headers: {
//       "Access-Control-Allow-Origin": "*",
//       "Access-Control-Allow-Methods": "PATCH, OPTIONS",
//       "Access-Control-Allow-Headers": "Content-Type, Authorization",
//     },
//   });
// }


export async function DELETE(_request: Request, { params }: { params: any }) {
  await connectDB();
  try {
    const { id } = params;
    if (!id) {
      throw new ApiError(400, "Mentor ID is required");
    }

    const deletedUser = await Mentor.findByIdAndDelete(id);
    const response: BaseResponse<MentorType> = {
      success: true,
      message: "Mentor deleted successfully",
      statusCode: 200,
      data: deletedUser,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET,PATCH, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    return errorHandler(error);
  }
}
