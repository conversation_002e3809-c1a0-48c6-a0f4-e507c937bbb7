// /src/app/api/skills/route.ts
import { NextResponse } from 'next/server';
import Service from '@/app/models/Service';
import { connectDB } from '../lib/db'
import { errorHandler } from '../utils/errorHandler';
import { ApiError } from '../utils/ApiError';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    await connectDB(); // Connect to MongoDB

    const { searchParams } = new URL(request.url);
    const nameQuery = searchParams.get('name') || '';
    const paginationParam = searchParams.get('pagination') || 'false';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const usePagination = paginationParam.toLowerCase() === 'true';

    // Base query with name filter
    let query = Service.find().sort({'_id': -1});
    
    if (nameQuery) {
      query = query.where('name').regex(new RegExp(nameQuery, 'i'));
    }

    query = query.lean();

    let data;

    if (usePagination) {
      // Get total count for pagination info
      const total = await Service.countDocuments(query.getFilter());
      
      // Apply pagination
      const services = await query.skip((page - 1) * limit).limit(limit).exec();

      data = {data: services, total};
    } else {
      // Return all results without pagination
      data = await query.exec();
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Services retrieved successfully'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      }
    });

  } catch (error: any) {
    console.error('Error fetching services:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve services',
      error: error.message
    }, {
      status: 500
    });
  }
}

export async function POST(request: Request) {
  await connectDB();

  try {
    const body = await request.json();
    const { title_en, title_ar } = body;

    // Validate required fields
    if (!title_en) {
      throw new ApiError(400, "English title is required");
    }

    // Check for duplicate title_en (case-insensitive)
    const existingService = await Service.findOne({
      title_en: { $regex: `^${title_en}$`, $options: "i" },
    });
    if (existingService) {
      throw new ApiError(400, `Service with title "${title_en}" already exists`);
    }

    // Create new service
    const newService = await Service.create({ title_en, title_ar: title_ar || null });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Service created successfully",
        data: newService,
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}



export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}