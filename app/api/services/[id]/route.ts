import mongoose from "mongoose";
import { ApiError } from "next/dist/server/api-utils";
import { NextResponse } from "next/server";
import { connectDB } from "../../lib/db";
import { errorHandler } from "../../utils/errorHandler";
import Service from "@/app/models/Service";


export async function DELETE(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Service ID is required");
    }

    // Find and delete the service
    const deletedService = await Service.findByIdAndDelete(id);
    if (!deletedService) {
      throw new ApiError(404, "Service not found");
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Service deleted successfully",
        data: deletedService,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}


export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;
    const body = await request.json();
    const { title_en, title_ar } = body;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Service ID is required");
    }

    // Validate input
    if (!title_en) {
      throw new ApiError(400, "English title is required");
    }

    // Check if service exists
    const service = await Service.findById(id);
    if (!service) {
      throw new ApiError(404, "Service not found");
    }

    // Check for duplicate title_en (case-insensitive, excluding current service)
    const existingService = await Service.findOne({
      title_en: { $regex: `^${title_en}$`, $options: "i" },
      _id: { $ne: id },
    });
    if (existingService) {
      throw new ApiError(400, `Service with title "${title_en}" already exists`);
    }

    // Update service
    service.title_en = title_en;
    service.title_ar = title_ar || null;
    const updatedService = await service.save();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Service updated successfully",
        data: updatedService,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}


export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}