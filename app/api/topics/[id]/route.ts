import mongoose from "mongoose";
import { ApiError } from "next/dist/server/api-utils";
import { NextResponse } from "next/server";
import { connectDB } from "../../lib/db";
import { errorHandler } from "../../utils/errorHandler";
import Topic from "@/app/models/Topic";



export async function DELETE(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Topic ID is required");
    }

    // Find and delete the topic
    const deletedTopic = await Topic.findByIdAndDelete(id);
    if (!deletedTopic) {
      throw new ApiError(404, "Topic not found");
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Topic deleted successfully",
        data: deletedTopic,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;
    const body = await request.json();
    const { title } = body;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Topic ID is required");
    }

    // Validate input
    if (!title) {
      throw new ApiError(400, "Title is required");
    }

    // Check if topic exists
    const topic = await Topic.findById(id);
    if (!topic) {
      throw new ApiError(404, "Topic not found");
    }

    // Check for duplicate title (case-insensitive, excluding current topic)
    const existingTopic = await Topic.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
      _id: { $ne: id },
    });
    if (existingTopic) {
      throw new ApiError(400, `Topic with title "${title}" already exists`);
    }

    // Update topic
    topic.title = title;
    const updatedTopic = await topic.save();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Topic updated successfully",
        data: updatedTopic,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}


export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}