// /src/app/api/skills/route.ts
import { NextResponse } from 'next/server';
import Topic from '@/app/models/Topic';
import { connectDB } from '../lib/db'
import { ApiError } from '../utils/ApiError';
import { errorHandler } from '../utils/errorHandler';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const nameQuery = searchParams.get('name') || '';
    const paginationParam = searchParams.get('pagination') || 'false';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const usePagination = paginationParam.toLowerCase() === 'true';

    // Base query with name filter
    let query = Topic.find();
    
    if (nameQuery) {
      query = query.where('title').regex(new RegExp(nameQuery, 'i'));
    }

    let data;

    if (usePagination) {
      // Get total count for pagination info
      const total = await Topic.countDocuments(query.getFilter());
      
      // Apply pagination
      const topics = await query.skip((page - 1) * limit).limit(limit).exec();

      data = {data: topics, total};
    } else {
      // Return all results without pagination
      data = await query.exec();
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Topics retrieved successfully'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
      }
    });

  } catch (error: any) {
    console.error('Error fetching topics:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve topics',
      error: error.message
    }, {
      status: 500
    });
  }
}

export async function POST(request: Request) {
  await connectDB();

  try {
    const body = await request.json();
    const { title } = body;

    // Validate required fields
    if (!title) {
      throw new ApiError(400, "Title is required");
    }

    // Check for duplicate title (case-insensitive)
    const existingTopic = await Topic.findOne({
      title: { $regex: `^${title}$`, $options: "i" },
    });
    if (existingTopic) {
      throw new ApiError(400, `Topic with title "${title}" already exists`);
    }

    // Create new topic
    const newTopic = await Topic.create({ title });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Topic created successfully",
        data: newTopic,
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}






export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}