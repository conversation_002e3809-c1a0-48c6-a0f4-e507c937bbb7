// /src/app/api/skills/route.ts
import { NextResponse } from 'next/server';
import Company from '../../models/Company';
import { connectDB } from '../lib/db'
import { ApiError } from '../utils/ApiError';
import { errorHandler } from '../utils/errorHandler';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    await connectDB(); // Connect to MongoDB

    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name') || '';
    const paginationParam = searchParams.get('pagination') || 'false';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const usePagination = paginationParam.toLowerCase() === 'true';

    // Base query with search
    let query = Company.find().sort({ '_id': -1 });;

    if (name) {
      query = query.where('name').regex(new RegExp(name, 'i'));
    }

    query = query.lean();

    let data;

    if (usePagination) {
      // Get total count for pagination info
      const total = await Company.countDocuments(query.getFilter());

      // Apply pagination
      const companies = await query.skip((page - 1) * limit).limit(limit).exec();

      data = { data: companies, total };
    } else {
      // Return all results without pagination
      data = await query.exec();
    }

    return NextResponse.json({
      success: true,
      data,
      message: 'Companies retrieved successfully'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      }
    });

  } catch (error: any) {
    console.error('Error fetching companies:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve companies',
      error: error.message
    }, {
      status: 500
    });
  }
}

export async function POST(request: Request) {
  await connectDB();

  try {
    const body = await request.json();
    const { name } = body;

    // Validate required fields
    if (!name) {
      throw new ApiError(400, "Name is required");
    }

    // Check for duplicate name (case-insensitive)
    const existingCompany = await Company.findOne({
      name: { $regex: `^${name}$`, $options: "i" },
    });
    if (existingCompany) {
      throw new ApiError(400, `Company with name "${name}" already exists`);
    }

    // Create new company
    const newCompany = await Company.create({ name });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Company created successfully",
        data: newCompany,
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}



export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}