import Company from "@/app/models/Company";
import mongoose from "mongoose";
import { ApiError } from "next/dist/server/api-utils";
import { NextResponse } from "next/server";
import { connectDB } from "../../lib/db";
import { errorHandler } from "../../utils/errorHandler";

export async function DELETE(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Company ID is required");
    }

    // Find and delete the company
    const deletedCompany = await Company.findByIdAndDelete(id);
    if (!deletedCompany) {
      throw new ApiError(404, "Company not found");
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Company deleted successfully",
        data: deletedCompany,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;
    const body = await request.json();
    const { name } = body;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Company ID is required");
    }

    // Validate input
    if (!name) {
      throw new ApiError(400, "Name is required");
    }

    // Check if company exists
    const company = await Company.findById(id);
    if (!company) {
      throw new ApiError(404, "Company not found");
    }

    // Check for duplicate name (case-insensitive, excluding current company)
    const existingCompany = await Company.findOne({
      name: { $regex: `^${name}$`, $options: "i" },
      _id: { $ne: id },
    });
    if (existingCompany) {
      throw new ApiError(400, `Company with name "${name}" already exists`);
    }

    // Update company
    company.name = name;
    const updatedCompany = await company.save();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Company updated successfully",
        data: updatedCompany,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH,OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}