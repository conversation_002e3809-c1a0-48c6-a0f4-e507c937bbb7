// /src/app/api/skills/route.ts
import { NextResponse } from 'next/server';
import AreaOfExpertise from '@/app/models/AreaOfExpertise';
import { connectDB } from '../lib/db'
import { ApiError } from '../utils/ApiError';
import { errorHandler } from '../utils/errorHandler';
import mongoose from 'mongoose';

export async function GET() {
  try {
    await connectDB();

    const experties = await AreaOfExpertise.find()

    return NextResponse.json({
      success: true,
      data: experties,
      message: 'Skills retrieved successfully'
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,POST OPTIONS'
      }
    });

  } catch (error: any) {
    console.error('Error fetching experties:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to retrieve experties',
      error: error.message
    }, {
      status: 500
    });
  }
}



export async function POST(request: Request) {
  await connectDB();

  try {
    const body = await request.json();
    const { title_en, title_ar } = body;

    // Validate required fields
    if (!title_en) {
      throw new ApiError(400, "English title is required");
    }

    // Check for duplicate title_en (case-insensitive)
    const existingArea = await AreaOfExpertise.findOne({
      title_en: { $regex: `^${title_en}$`, $options: "i" },
    });
    if (existingArea) {
      throw new ApiError(400, `Area of Expertise with title "${title_en}" already exists`);
    }

    // Create new area of expertise
    const newArea = await AreaOfExpertise.create({ title_en, title_ar: title_ar || null });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Area of Expertise created successfully",
        data: newArea,
      },
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}







export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}