import mongoose from "mongoose";
import { ApiError } from "next/dist/server/api-utils";
import { NextResponse } from "next/server";
import { connectDB } from "../../lib/db";
import { errorHandler } from "../../utils/errorHandler";
import AreaOfExpertise from "@/app/models/AreaOfExpertise";
export async function DELETE(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Area of Expertise ID is required");
    }

    // Find and delete the area of expertise
    const deletedArea = await AreaOfExpertise.findByIdAndDelete(id);
    if (!deletedArea) {
      throw new ApiError(404, "Area of Expertise not found");
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Area of Expertise deleted successfully",
        data: deletedArea,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}


export async function PATCH(request: Request, { params }: { params: any }) {
  await connectDB();

  try {
    const { id } = params;
    const body = await request.json();
    const { title_en, title_ar } = body;

    // Validate ID
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new ApiError(400, "Valid Area of Expertise ID is required");
    }

    // Validate input
    if (!title_en) {
      throw new ApiError(400, "English title is required");
    }

    // Check if area of expertise exists
    const area = await AreaOfExpertise.findById(id);
    if (!area) {
      throw new ApiError(404, "Area of Expertise not found");
    }

    // Check for duplicate title_en (case-insensitive, excluding current area)
    const existingArea = await AreaOfExpertise.findOne({
      title_en: { $regex: `^${title_en}$`, $options: "i" },
      _id: { $ne: id },
    });
    if (existingArea) {
      throw new ApiError(400, `Area of Expertise with title "${title_en}" already exists`);
    }

    // Update area of expertise
    area.title_en = title_en;
    area.title_ar = title_ar || null;
    const updatedArea = await area.save();

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: "Area of Expertise updated successfully",
        data: updatedArea,
      },
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, DELETE, PATCH, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type",
        },
      }
    );
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}