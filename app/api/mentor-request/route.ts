

// app/api/mentor-request/route.js
import { createClient } from 'contentful-management';
import { NextResponse } from 'next/server';
import { Readable } from 'stream';

export async function POST(req: Request) {
  try {
    const token = process.env.CONTENTFUL_MANAGEMENT_TOKEN;
    if (!token) {
      throw new Error('CONTENTFUL_MANAGEMENT_TOKEN is not defined');
    }

    const client = createClient({
      accessToken: token
    });

    const spaceId = process.env.CONTENTFUL_SPACE_ID;
    if (!spaceId) {
      throw new Error('CONTENTFUL_SPACE_ID is not defined');
    }
    const space = await client.getSpace(spaceId);
    const env = await space.getEnvironment('master');
    const formData = await req.formData();

    const name = formData.get("name");
    const email = formData.get("email");
    const mentorReason = formData.get("mentorReason");
    const details = formData.get("details") || "Not Provided";
    const linkedin = formData.get("linkedin");
    const notes = formData.get("notes");
    const cvFile = formData.get("cvFile");

    if (!name || !email || !mentorReason) {
      return NextResponse.json(
        { error: 'Name, email, and mentor reason are required' },
        { status: 400 }
      );
    }

    if (mentorReason === 'other' && !details) {
      return NextResponse.json(
        { error: 'Please provide us with more details' },
        { status: 400 }
      );
    }

    // Prepare entry data
    const entryFields: {
      fields: {
        name: { 'en-US': FormDataEntryValue };
        email: { 'en-US': FormDataEntryValue };
        mentorReason: { 'en-US': FormDataEntryValue };
        details: { 'en-US': FormDataEntryValue };
        linkedin?: { 'en-US': FormDataEntryValue };
        notes?: { 'en-US': FormDataEntryValue };
        cv?: { 'en-US': { sys: { type: string; linkType: string; id: string } } };
      }
    } = {
      fields: {
        name: { 'en-US': name },
        email: { 'en-US': email },
        mentorReason: {
          'en-US': mentorReason
        },
        details: {
          'en-US': details
        }
      }
    };

    if (linkedin) entryFields.fields.linkedin = { 'en-US': linkedin };
    if (notes) entryFields.fields.notes = { 'en-US': notes };

    if (cvFile && typeof cvFile !== 'string' && 'size' in cvFile && cvFile.size > 0) {
      const arrayBuffer = await cvFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Get actual MIME type from the file
      const contentType = cvFile.type || 'application/octet-stream';

      const asset = await env.createAssetFromFiles({
        fields: {
          title: { 'en-US': `CV - ${name}` },
          description: { 'en-US': `CV uploaded by ${name}` },
          file: {
            'en-US': {
              contentType: contentType,
              fileName: cvFile.name,
              file: Readable.from(buffer), // Convert buffer to stream
            },
          },
        },
      });

      const processedAsset = await asset.processForAllLocales();
      await processedAsset.publish();

      entryFields.fields.cv = {
        'en-US': {
          sys: {
            type: 'Link',
            linkType: 'Asset',
            id: asset.sys.id,
          },
        },
      };
    }

    const entry = await env.createEntry('mentorRequest', entryFields);
    await entry.publish();

    return NextResponse.json({ success: true }, {
      status: 201,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },

    });

  } catch (error) {
    console.error('Submission failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        details: typeof error === 'object' && error !== null && 'response' in error ? (error as any).response?.data : undefined
      },
      { status: 500 }
    );
  }
}


export async function OPTIONS() {
  return new Response(null, {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
