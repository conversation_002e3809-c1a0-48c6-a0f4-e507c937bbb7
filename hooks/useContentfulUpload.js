


import { useState } from 'react';
import { createClient } from 'contentful-management';
import { toast } from 'react-hot-toast';
import axios from 'axios';

export const useSubmitMentorRequest = () => {
  const [isLoading, setIsLoading] = useState(false);

  const submit = async (formData) => {
    setIsLoading(true);


    try {
      const { data } = await axios.post('/api/mentor-request', formData)

      return data
    } catch (error) {
      console.error('Submission failed:', error);
      toast.error('Submission failed , please try again');
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  return { submit, isLoading };
};