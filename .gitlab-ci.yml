stages:
  - prohelpify

variables:
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
  GIT_STRATEGY: clone

prohelpify:
  stage: prohelpify
  script:
    - cd /var/www/html/prohelpify-website-revamp
    - sudo git stash
    - echo "live"
    - sudo git pull --rebase origin main
    - echo "Pull From Main Branch"
    - sudo docker build -t prohelpify-revamp .
    - sudo docker rm -f prohelpify-revamp && sudo docker run -it --name prohelpify-revamp   --restart=always -p 2028:3000 -d prohelpify-revamp
  tags:
    - main
  only:
    - main
