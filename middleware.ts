import createMiddleware from 'next-intl/middleware';
import {routing} from './i18n/routing';
import {NextRequest} from 'next/server';

const intlMiddleware = createMiddleware(routing);

const publicApiRoutes = [
  '/api/auth/register',
  '/api/auth/login',
  '/api/auth/change-password',
  '/api/auth/complete-profile',
  '/api/mentors',
  '/api/mentors/:id',
  '/api/skills',
  '/api/topics',
  '/api/companies',
  '/api/experties',
  '/api/services',
  '/api/mentor-request',
  // Add other public API routes here
];

export default function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Skip specific API routes
  if (publicApiRoutes.some(route => pathname.startsWith(route))) {
    return;
  }
  
  return intlMiddleware(request);
}

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/"],
};