import { FilterOptions } from "@/lib/type";
import { Facebook } from "lucide-react";
import { useState } from "react";

export const NavItems: string[] = ["Practices", "Team", "Join", "Contact us"];

export interface Technology {
  id: number;
  title: string;
  icon: string;
}

export const technologies: Technology[] = [
  { id: 1, title: "Software Engineering", icon: "/assists/icons/Git.svg" },
  { id: 2, title: "Web Development", icon: "/assists/icons/JS.svg" },
  { id: 3, title: "Mobile Development", icon: "/assists/icons/Flutter.svg" },
  { id: 4, title: "Machine Learning and AI", icon: "/assists/icons/Ai.svg" },
  { id: 5, title: "Data Science", icon: "/assists/icons/Data Science.svg" },
  { id: 6, title: "DevOps Engineering", icon: "/assists/icons/DevOps.svg" },
];

export interface SocialMediaItem {
  id: number;
  name:string;
  icon: string;
  link: string;
  className?: string;
}

export const FooterSocialMedia: SocialMediaItem[] = [
  {
    id: 1,
    name:"Facebook",
    icon: "/assists/icons/Face.svg",
    link: "https://www.facebook.com/share/1DcAx8XAnT/",
  },
  {
    id: 2,
    name:"YouTube",
    icon: "/assists/icons/youtube.svg",
    link: "https://www.youtube.com/@p8y-channel",
  },
  {
    id: 3,
    name:"Linkedin",
    icon: "/assists/icons/linkedin.svg",
    link: "https://www.linkedin.com/showcase/prohelpify",
  },
];

export interface Company {
  name: string;
  logo: string;
  website: string;
}

export const companies: Company[] = [
  { name: "Meta", logo: "/assists/brands/meta-logo.webp", website: "https://about.meta.com/" },
  { name: "Tradinos", logo: "/assists/brands/tradinos-logo.webp", website: "https://tradinos.com/" },
  { name: "Beeorder", logo: "/assists/brands/beeorder-logo.webp", website: "https://www.beeorder.com/" },
  { name: "Kaiko", logo: "/assists/brands/kaiko-logo.webp", website: "https://www.kaikosystems.com/" },
  { name: "Glovo", logo: "/assists/brands/glovo-logo.webp", website: "https://glovoapp.com/" },
  { name: "l-one Systems", logo: "/assists/brands/l-one-logo.webp", website: "https://www.l-one.de/en-us" },
  { name: "Revolt", logo: "/assists/brands/revolut-logo.webp", website: "https://www.revolut.com/" },
  { name: "Innopolis", logo: "/assists/brands/innopolis-logo.webp", website: "https://innopolis.university/en/" },
  { name: "Skoltech", logo: "/assists/brands/skol-logo.webp", website: "http://www.skoltech.ru/en" },
  { name: "Stowk", logo: "/assists/brands/stowk-logo.webp", website: "http://www.stowk.com/" },
  { name: "Apaleo", logo: "/assists/brands/apaleo-logo.webp", website: "https://apaleo.com/" },
  { name: "Baxenergy", logo: "/assists/brands/baxenergy-logo.webp", website: "https://www.baxenergy.com/" },
  { name: "Concord4Solutions", logo: "/assists/brands/concord-logo.webp", website: "https://concord4solutions.com/" },
  { name: "Newswav", logo: "/assists/brands/newswav-logo.webp", website: "https://newswav.com/" },
  { name: "Canny Tech", logo: "/assists/brands/cannytexh-logo.webp", website: "https://cannytechs.com/#/home/<USER>" },
  { name: "It plus24", logo: "/assists/brands/itplus24-logo.webp", website: "https://www.it-plus24.nl/" },
  { name: "Brain Socket", logo: "/assists/brands/brainsocket-logo.webp", website: "https://brain-socket.com/" },
  { name: "Genophore", logo: "/assists/brands/genophore-logo.webp", website: "https://genophore.com/" },
  { name: "Beinmedia", logo: "/assists/brands/beinmedia-Logo.webp", website: "http://www.beinmedia.com" },
  { name: "Blue-logic-digital", logo: "/assists/brands/blue-logic-digital-logo.webp", website: "https://bluelogic.ai/" },
  { name: "Infinite-info", logo: "/assists/brands/infinite-info-logo.webp", website: "https://infiniteinfo.co/" },
  { name: "madewithlove", logo: "/assists/brands/madewithlove-logo.webp", website: "https://madewithlove.com/" },
  { name: "Omnix", logo: "/assists/brands/omnix-logo.webp", website: "https://omnix.com/" },
  { name: "StorexWeb", logo: "/assists/brands/StorexWeb-logo.webp", website: "https://storexweb.com/ar/" },
];