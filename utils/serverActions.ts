"use server"

interface MentorFilters {
    search?: string;
    skill?: string;
    service?: string;
    topic?: string;
    expertise?: string;
    company?: string;
    level?: string;
    english_only?: boolean;
    page?: number;
    limit?: number;
  }
  

export async function getMentors(filters: MentorFilters) {
  try {
    const queryParams = new URLSearchParams();
    
    // Add filters to query params if they exist
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${process.env.NEXT_PUBLIC_API_URL}/api/mentors?${queryParams.toString()}`;
    
    const response = await fetch(url, {
      method: "GET",
      headers: { 
        "Content-Type": "application/json",
      },
      next: { revalidate: 30 },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch mentors: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching mentors:', error);
    throw error;
  }
}


  export async function fetchSkills() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/skills`, {
        method: "GET",
        headers: { 
          "Content-Type": "application/json",
        },
        next: { revalidate: 30 },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch skills: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching skills:', error);
      throw error;
    }
  }

  export async function fetchAreasOfExpertise() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/experties`, {
        method: "GET",
        headers: { 
          "Content-Type": "application/json",
        },
        next: { revalidate: 30 },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch areas of expertise: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching areas of expertise:', error);
      throw error;
    }
  }

  export async function fetchTopics() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/topics`, {
        method: "GET",
        headers: { 
          "Content-Type": "application/json",
        },
        next: { revalidate: 30 },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch topics: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching topics:', error);
      throw error;
    }
  }

  export async function fetchCompanies() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/companies`, {
        method: "GET",
        headers: { 
          "Content-Type": "application/json",
        },
        next: { revalidate: 30 },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch companies: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching companies:', error);
      throw error;
    }
  }

  export async function fetchServices() {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/services`, {
        method: "GET",
        headers: { 
          "Content-Type": "application/json",
        },
        next: { revalidate: 30 },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch services: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching services:', error);
      throw error;
    }
  }
  
  
  