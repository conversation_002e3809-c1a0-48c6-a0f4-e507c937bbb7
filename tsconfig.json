{"compilerOptions": {"target": "es5", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "module": "esnext", "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "next-sitemap.config.js", "components/ui/select.tsx", "components/ui/Combobox.tsx", "components/Landing.jsx", "components/ClarityProvider.js"]}