//mentors filter type


export type Mentor = {
  _id: string;
  name_en: string;
  name_ar: string;
  profile_image: {
    url: string,
  };
  level: string;
  current_company?: {
    _id: string;
    name: string;
  };
  skills: {
    _id: string;
    title: string;
  }[];
  areas_of_expertise: {
    title_en: string;
    title_ar: string | null;
  }[];
  services: {
    _id: string;
    title_en: string;
    title_ar: string;
  }[];
  topics: {
    _id: string;
    title: string;
  }[];
  english_session: boolean;
  gender: string;
  bio_en: string;
  bio_ar: string;
};

export interface GenderOption {
  _id: string;
  title: string;
}

export type FilterOptions = {
  techSkills: { _id: string; title: string }[];
  expertise: { _id: string; title_en: string; title_ar: string }[];
  services: { _id: string; title_en: string; title_ar: string }[];
  companies: { _id: string; name: string }[];
  topics: { _id: string; title: string }[];
  gender: GenderOption[];  // Now properly typed
  levels: string[];
  englishSessionOnly: boolean;
}

export type Skill = {
  title: string;
};

export type AreaOfExpertise = {
  title_en: string;
  title_ar?: string | null;
};

export type Topic = {
  title: string;
};

export type Company = {
  name: string;
};