import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  const locale = (await requestLocale) || routing.defaultLocale;

  try {
    const messages = (await import(`@/messages/${locale}.json`)).default;
    return {
      locale,
      messages,
    };
  } catch (error) {
    console.error(`Error loading messages for locale: ${locale}`, error);
    const fallbackMessages = (await import(`@/messages/en.json`)).default;
    return {
      locale: 'en',
      messages: fallbackMessages,
    };
  }
});
