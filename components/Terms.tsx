import { useTranslations } from 'next-intl'
import React from 'react'


interface TermsProps {
  isRTL: boolean;
}

export const Terms: React.FC<TermsProps> = ({ isRTL }) => {
  const terms = useTranslations("terms")
  return (
    <main className={`flex flex-col md:m-16 m-5 ${isRTL ? "text-right" : "text-left"}`}>
      <header className="flex flex-col gap-2">
        <h1 className="md:text-[32px] text-[20px] font-bold">{terms("title")}</h1>
        <p className="md:text-[18px] text-[16px]">{terms("description")}</p>
      </header>
      {[1, 2, 3, 4, 5, 6, 7].map((index) => {
        return (
          <section className="md:p-5 p-2 flex flex-col gap-2" key={index}>
            <h2 className="font-semibold md:text-[22px] text-[18px]">{`${index}.${terms(
              `${index}.title`
            )} :`}</h2>
            <p className="text-[18px]">{terms(`${index}.description`)}</p>
            <div className="flex flex-col gap-2">
              {[1, 2, 3].map((sub, i) => {
                return (
                  <div key={sub + i} className="flex flex-col gap-2 md:px-8">
                    <h3 className="font-bold">
                      {terms(`${index}.${sub}.title`)}{" "}
                    </h3>
                    <p className="md:px-4">
                      {terms(`${index}.${sub}.description`)}
                    </p>
                  </div>
                );
              })}
            </div>
          </section>
        );
      })}
      <p className="mt-10">{terms("footer")}</p>
    </main>
  );
};
