"use client";

import React from "react";
import Image from "next/image";

interface TechItem {
  icon: string;
  title: string;
}

interface TechsContinuousLoopProps {
  items: TechItem[];
  isRTL?: boolean;
}

const TechsContinuousLoop: React.FC<TechsContinuousLoopProps> = ({ items, isRTL }) => {
  // Create enough duplicates to ensure seamless infinite scroll
  // We need at least 3 sets to ensure no visible seam during animation
  const duplicatedItems = [...items, ...items, ...items];

  return (
    <div className="relative overflow-hidden w-full h-20">
      <div
        className={`flex gap-5 ${isRTL ? "animate-infinite-scroll-rtl" : "animate-infinite-scroll"}`}
        style={{
          width: 'max-content'
        }}
      >
        {duplicatedItems.map((item, index) => (
          <div
            key={`${item.title}-${index}`}
            className="flex items-center justify-center min-w-[200px] px-4 py-2 bg-gray-800/30 rounded-lg border border-gray-700/50 backdrop-blur-sm hover:border-main_color/50 transition-all duration-300 flex-shrink-0"
          >
            <Image
              src={item.icon}
              alt={item.title}
              width={32}
              height={32}
              className={`${isRTL ? 'ml-3' : 'mr-3'} flex-shrink-0`}
              loading="lazy"
            />
            <p className="text-sm font-semibold text-white whitespace-nowrap">
              {item.title}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

interface ContinuousLoopContainerProps {
  items: TechItem[];
  isRTL?: boolean;
}

const ContinuousLoopContainer: React.FC<ContinuousLoopContainerProps> = ({ items, isRTL = false }) => {
  return <TechsContinuousLoop items={items} isRTL={isRTL} />;
};

export default ContinuousLoopContainer;
