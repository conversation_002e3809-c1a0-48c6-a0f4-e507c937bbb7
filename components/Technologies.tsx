import React from "react";
import { technologies } from "@/utils/constant";
import ContinuousLoopContainer from "./TechsContinuousLoop";
import { useTranslations } from "next-intl";

const Technologies = ({isRTL}:{isRTL:boolean}) => {
    const t = useTranslations("tech");

    return (
        <main id="technologies" className="relative container max-lg:px-4 max-sm:px-3">
            <h2 className="text-4xl text-white text-center font-bold mb-20">
                {t("title")}
            </h2>
            <div className="flex gap-5 relative">
                <ContinuousLoopContainer items={technologies} isRTL={isRTL} />
            </div>
        </main>
    );
};

export default Technologies;
