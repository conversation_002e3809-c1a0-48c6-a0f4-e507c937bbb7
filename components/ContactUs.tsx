import React from "react";
import Image from "next/image";
import Button from "./Button";
import { useTranslations } from "next-intl";

interface ContactUsProps {
  isRTL: boolean;
}

const ContactUs: React.FC<ContactUsProps> = ({ isRTL }) => {
  const contact = useTranslations("contactUs");

  return (
    <main id="contact us" className="contact-main section-padding">
      <div
        className={`absolute -bottom-[50px] ${
          isRTL ? "-left-20" : "-right-20"
        } -z-10 overflow-hidden h-[40rem] w-[40rem] bg-custom-radial-purple`}
      />

      <div className="contact-container px-4 lg:px-0">
        <div
          className={`flex flex-col justify-between gap-5 w-4/5 max-md:mx-auto ${
            isRTL ? "md:text-right" : "md:text-left"
          }`}
        >
          <h2 className="contact-h2" aria-label="Contact Us - Prohelpify">
            {contact("contactUs_title")}
          </h2>
          <h3 className="contact-h3">{contact("contactUs_quastion")}</h3>

          <p
            className={`max-md:w-5/6 max-md:text-center max-lg:w-10/12 w-4/6 text-text_color ${
              isRTL ? "text-right" : "text-left"
            } font-normal text-[1rem] max-md:text-sm mb-6 max-md:mx-auto`}
          >
            {contact("contactUs_desc")}
          </p>

          <a
            target="_blank"
            rel="noopener noreferrer"
            href="https://docs.google.com/forms/d/e/1FAIpQLSf9ZbRP3Larh_q2WD6YPyxU7bUsFrjwbR43y07YM65-RIKmTA/viewform"
            className="max-md:mx-auto flex max-md:justify-center w-fit"
            aria-label="Contact Us Form"
          >
            <Button title={contact("contactUs_button_title")} />
          </a>
        </div>

        <div className="relative w-2/5 max-md:w-full h-56 md:h-80 max-md:mx-auto">
          {contact("contactUs_image") ? (
            <Image
              src={contact("contactUs_image") as string}
              alt="Contact Us - Prohelpify"
              width={200}
              height={200}
              loading="lazy"
              className="rounded-lg max-md:mx-auto w-full h-full"
            />
          ) : (
            <p className="text-red-500">Image not found</p>
          )}
        </div>
      </div>

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebPage",
          name: "Contact Us - Prohelpify",
          description:
            "Get in touch with Prohelpify to learn coding, debugging, and best practices from our team of senior programmers.",
          url: "https://www.example.com/contact-us",
          image: "https://www.example.com/og-image.jpg",
        })}
      </script>
    </main>
  );
};

export default ContactUs;
