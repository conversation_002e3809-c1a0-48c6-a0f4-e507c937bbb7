import React from "react";
import { useTranslations } from "next-intl";

interface PrivacyProp{
  isRTL:boolean;
}

const Privacy : React.FC<PrivacyProp> = ({ isRTL }) => {
  const privacy = useTranslations("privacy");
  return (
    <main className={`flex flex-col md:m-16 m-6 ${isRTL ? "text-right" : "text-left"}`}>
      <header className="flex flex-col gap-2">
        <h1 className="md:text-[32px] text-[20px] font-bold">{privacy("title")}</h1>
        <p className="text-[16px]">{privacy("description")}</p>
      </header>
      {[1, 2, 3, 4, 5, 6, 7].map((index) => {
        return (
          <section className="md:p-5 p-2 flex flex-col gap-2" key={index}>
            <h2 className="font-semibold text-[16px]">{`${index}.${privacy(
              `${index}.title`
            )} :`}</h2>
            <p className="text-[18px]">{privacy(`${index}.description`)}</p>
            <div className="flex flex-col gap-2">
              {[1, 2, 3].map((sub, i) => {
                return (
                  <div key={sub + i} className="flex flex-col gap-2 sm:px-8 ">
                    <h3 className="font-bold">
                      {privacy(`${index}.${sub}.title`)}{" "}
                    </h3>
                    <p className="sm:px-4">
                      {privacy(`${index}.${sub}.description`)}
                    </p>
                  </div>
                );
              })}
            </div>
          </section>
        );
      })}
      <p className="mt-10">{privacy("footer")}</p>
    </main>
  );
};

export default Privacy;