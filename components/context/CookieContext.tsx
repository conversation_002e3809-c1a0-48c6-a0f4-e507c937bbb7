"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { setCookie, getCookie, deleteCookie } from "cookies-next";

interface CookieContextType {
  dialogOpened: boolean;
  updateDialogOpened: (value: boolean) => void;
  clearDialogOpened: () => void;
}

const CookieContext = createContext<CookieContextType | undefined>(undefined);

interface CookieProviderProps {
  children: ReactNode;
}

export const CookieProvider: React.FC<CookieProviderProps> = ({ children }) => {
  const [dialogOpened, setDialogOpened] = useState<boolean>(false);

  useEffect(() => {
    const storedValue = getCookie("dialogOpened") === "true";
    setDialogOpened(storedValue);
  }, []);

  const updateDialogOpened = (value: boolean) => {
    setDialogOpened(value);
    setCookie("dialogOpened", value.toString(), { maxAge: 60 * 60 * 24 * 7 }); // صلاحية 1 أسبوع
  };

  const clearDialogOpened = () => {
    setDialogOpened(false);
    deleteCookie("dialogOpened");
  };

  return (
    <CookieContext.Provider value={{ dialogOpened, updateDialogOpened, clearDialogOpened }}>
      {children}
    </CookieContext.Provider>
  );
};

export const useCookie = (): CookieContextType => {
  const context = useContext(CookieContext);
  if (!context) {
    throw new Error("useCookie must be used within a CookieProvider");
  }
  return context;
};
