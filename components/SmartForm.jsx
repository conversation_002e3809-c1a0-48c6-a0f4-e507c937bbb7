"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";
import { toast, Toaster } from 'react-hot-toast';
import { useParams } from "next/navigation";
import { useSubmitMentorRequest } from "@/hooks/useContentfulUpload"
import Spinner from "./Spinner";
import { ArrowRight, ArrowLeft, Check, User, FileText, CheckCircle } from "lucide-react";
import { DialogClose } from "@/components/ui/dialog";
const steps = ["Mentor Reason", "Basic Info", "Success"];

export default function MentorWizard({ initialReason }) {
    const { submit, isLoading } = useSubmitMentorRequest();
    const { locale } = useParams();
    const isRTL = locale === 'ar';
    const t = useTranslations("wizard");
    // If initialReason is provided, start at step 1 (Basic Info), otherwise start at step 0 (Reason selection)
    const [step, setStep] = useState(initialReason ? 1 : 0);
    const [form, setForm] = useState({
        name: "",
        email: "",
        mentorReason: initialReason || "",
        details: "",
        linkedin: "",
        notes: "",
        cvFile: null,
    });

    // State to track validation errors
    const [errors, setErrors] = useState({
        email: "",
    });

    const nextStep = () => setStep((s) => Math.min(s + 1, steps.length - 1));
    const prevStep = () => setStep((s) => Math.max(s - 1, 0));

    // Validate email format
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const updateForm = (field, value) => {
        setForm((f) => ({ ...f, [field]: value }));

        // Validate email when it's updated
        if (field === 'email') {
            if (!value.trim()) {
                setErrors(prev => ({ ...prev, email: t("error.email_required", { defaultValue: "Email is required" }) }));
            } else if (!validateEmail(value)) {
                setErrors(prev => ({ ...prev, email: t("error.invalid_email", { defaultValue: "Please enter a valid email address" }) }));
            } else {
                setErrors(prev => ({ ...prev, email: "" }));
            }
        }
    };

    const isNextDisabled = () => {
        if (step === 0) return !form.mentorReason;
        // || !form.details;
        if (step === 1) {
            // Check for required fields and validation errors
            return !form.name || !form.email || errors.email !== "";
        }
        return false;
    };

    // Handle RTL layout adjustments as needed
    const handleSubmit = async () => {
        try {
            // Validate email before submitting
            if (!validateEmail(form.email)) {
                setErrors(prev => ({ ...prev, email: t("error.invalid_email", { defaultValue: "Please enter a valid email address" }) }));
                return;
            }

            const formData = new FormData()
            formData.append('name', form.name)
            formData.append('email', form.email)
            formData.append('mentorReason', form.mentorReason)
            formData.append('details', form.details)
            formData.append('linkedin', form.linkedin)
            formData.append('notes', form.notes)
            formData.append('cvFile', form.cvFile)
            const result = await submit(formData);

            if (result.success) {
                // Move to success step instead of showing dialog
                setStep(2);
                // We'll keep the form data in case we need to go back
            } else {
                toast.error(result.error || t("error.submission_failed", { defaultValue: "Failed to submit your request. Please try again." }));
            }
        } catch (error) {
            console.error('Submission error:', error);
            toast.error(error.message || t("error.unexpected", { defaultValue: "An unexpected error occurred" }));
        }
    };

    // Stepper icons
    const stepIcons = [User, FileText, CheckCircle];

    // Get reason display text
    const getReasonText = (reason) => {
        if (!reason) return "";
        const reasonKey = `reasons.${reason === "interview" ? "1" : reason === "consultation" ? "2" : reason === "roadmap" ? "3" : "4"}`;
        return t(reasonKey);
    };

    return (
        <>
            <Toaster position="top-center" />
            <div className="w-full max-w-4xl h-full mx-auto flex flex-col justify-between">
                {/* Stepper */}
                <div className="flex items-center justify-center mb-8 px-4">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                        {steps.map((stepName, index) => {
                            const StepIcon = stepIcons[index];
                            const isActive = index === step;
                            const isCompleted = index < step;
                            const isClickable = index <= step || (initialReason && index === 0);

                            return (
                                <div key={index} className="flex items-center">
                                    <div
                                        onClick={() => {
                                            if (isClickable) {
                                                setStep(index);
                                            }
                                        }}
                                        title={index === 0 && form.mentorReason ? getReasonText(form.mentorReason) : t(`step_${index + 1}`, { defaultValue: stepName })}
                                        className={`
                                            relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300
                                            ${isActive
                                                ? 'bg-main_color border-main_color text-white shadow-lg shadow-main_color/30'
                                                : isCompleted
                                                    ? 'bg-main_color border-main_color text-white shadow-lg shadow-main_color/30'
                                                    : 'bg-gray-700 border-gray-600 text-gray-400'
                                            }
                                            ${isClickable ? 'cursor-pointer hover:scale-110 hover:shadow-lg' : 'cursor-not-allowed'}
                                        `}
                                    >
                                        {isCompleted ? (
                                            <Check className="w-6 h-6" />
                                        ) : (
                                            <StepIcon className="w-6 h-6" />
                                        )}
                                    </div>

                                    {/* Connector Line */}
                                    {index < steps.length - 1 && (
                                        <div className={`w-12 md:w-16 h-0.5 mx-3 md:mx-4 transition-colors duration-300 ${index < step ? 'bg-main_color' : 'bg-gray-600'}`} />
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </div>

                <div className="w-full max-w-lg mx-auto flex flex-col justify-between">
                {/* <CardContent className="w-full space-y-6"> */}
                {/* <h2 className="text-4xl max-md:text-2xl font-bold mb-6 text-center text-white ">
                        {t("title_1")} <span className="text-main_color">{t("title_2")}</span> {t("title_3")}
                    </h2> */}

                {step === 0 && (
                    <div className="space-y-4">
                        <Label className={`text-lg font-semibold  text-white ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t("reason.question_1")}
                            <span className="text-red-500"> *</span>
                        </Label>
                        <RadioGroup
                            value={form.mentorReason}
                            onValueChange={(val) => updateForm("mentorReason", val)}
                            className="flex flex-col gap-3 mt-4"
                        >
                            {["interview", "consultation", "roadmap", "other"].map((key, i) => (
                                <div
                                    key={i}
                                    className={`
                                        flex items-center justify-center p-3 rounded-lg border-2
                                        ${form.mentorReason === key
                                            ? 'border-main_color bg-main_color/10'
                                            : 'border-gray-600 hover:border-gray-400'
                                        }
                                        cursor-pointer transition-all duration-200
                                    `}
                                    onClick={() => updateForm("mentorReason", key)}
                                >
                                    <div className="text-center">
                                        <p className="font-medium text-white">{t(`reasons.${i + 1}`)}</p>
                                    </div>
                                </div>
                            ))}
                        </RadioGroup>

                        {/* {form.mentorReason && (
                            <div className="flex flex-col gap-3 ">
                                <Label htmlFor="other-mentor" className="text-lg  text-white font-semibold w-full">
                                    {t("reason.question_2")}


                                </Label>
                                <Textarea
                                    id="other-mentor"
                                    value={form.details}
                                    onChange={(e) => updateForm("details", e.target.value)}
                                    className="w-full  text-white"
                                />
                            </div>
                        )} */}
                    </div>
                )}

                {step === 1 && (
                    <div className={`space-y-3  text-white  ${isRTL ? 'text-right' : ''}`}>
                        <div>
                            <Label htmlFor="name">{t("info.name")}
                                <span className="text-red-500"> *</span>
                            </Label>
                            <Input
                                id="name"
                                value={form.name}
                                className="bg-transparent"
                                onChange={(e) => updateForm("name", e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="email">{t("info.email")}
                                <span className="text-red-500"> *</span>
                            </Label>
                            <Input
                                id="email"
                                type="email"
                                value={form.email}
                                className={`bg-transparent ${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                                onChange={(e) => updateForm("email", e.target.value)}
                                onBlur={(e) => {
                                    // Validate on blur to catch empty fields
                                    if (!e.target.value.trim()) {
                                        setErrors(prev => ({ ...prev, email: t("error.email_required", { defaultValue: "Email is required" }) }));
                                    }
                                }}
                            />
                            {errors.email && (
                                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                            )}
                        </div>
                        <div>
                            <Label htmlFor="linkedin" > {t("cv_linkedin.linkedin")}</Label>
                            <Input
                                id="linkedin"
                                type="url"
                                placeholder="https://linkedin.com/in/your-profile"
                                value={form.linkedin}
                                onChange={(e) => updateForm("linkedin", e.target.value)}
                                className="text-white placeholder:text-gray-500 placeholder:font-semibold"
                            />
                        </div>
                        <div>
                            <Label htmlFor="cvFile">{t("cv_linkedin.cv")}</Label>
                            <Input
                                id="cvFile"
                                type="file"
                                accept=".pdf,.doc,.docx"
                                className="text-white file:text-gray-500 file:mr-4 file:py-1 file:px-2 cursor-pointer  file:border-0 file:text-sm file:font-semibold"
                                onChange={(e) => updateForm("cvFile", e.target.files?.[0] || null)}
                            />
                            {form.cvFile && <p className="text-sm mt-2">Selected: {form.cvFile.name}</p>}
                        </div>
                        <div className="flex flex-col gap-1">
                            <Label htmlFor="notes" className="">
                                {t("cv_linkedin.notes")}
                            </Label>
                            <Textarea
                                id="notes"
                                value={form.notes}
                                placeholder={t("sharing_details")}
                                onChange={(e) => updateForm("notes", e.target.value)}
                            />
                        </div>
                    </div>
                )}

                {step === 2 && (
                    <div className="flex flex-col items-center justify-center text-center space-y-6 py-8">
                        <div className="w-16 h-16 bg-main_color/20 rounded-full flex items-center justify-center">
                            <Check className="w-8 h-8 text-main_color" />
                        </div>
                        <h3 className="text-2xl font-bold text-main_color">
                            {t("success")}
                        </h3>
                        <p className="text-white text-lg max-w-md">
                            {t("success_message")}
                        </p>
                    </div>
                )}
                <div className="flex justify-between w-full gap-10 mt-6">
                    {step === 1 ? (
                        <Button
                            variant="outline"
                            disabled={isLoading}
                            onClick={prevStep}
                            className={`md:w-1/4 w-1/2 flex items-center justify-center gap-2 group`}
                        >
                            {isRTL ? (
                                <>
                                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                                    <span>{t("back_button")}</span>
                                </>
                            ) : (
                                <>
                                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                                    <span>{t("back_button")}</span>
                                </>
                            )}
                        </Button>
                    ) : step === 0 ? (
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="!border-gray-600 hover:!border-gray-400 text-white hover:text-white hover:bg-gray-700 md:w-1/4 w-1/2 flex items-center justify-center"
                            >
                                {t("cancel", { defaultValue: "Cancel" })}
                            </Button>
                        </DialogClose>
                    ) : (
                        <div></div> // Empty div for spacing on success step
                    )}

                    {step === 0 ? (
                        <Button
                            onClick={nextStep}
                            disabled={isNextDisabled()}
                            className="bg-main_color hover:bg-main_color md:w-1/4 w-1/2 flex items-center justify-center gap-2 group"
                        >
                            {isRTL ? (
                                <>
                                    <span>{t("next_button")}</span>
                                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                                </>
                            ) : (
                                <>
                                    <span>{t("next_button")}</span>
                                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                                </>
                            )}
                        </Button>
                    ) : step === 1 ? (
                        <Button
                            disabled={isNextDisabled() || isLoading}
                            onClick={handleSubmit}
                            className="bg-main_color hover:bg-main_color md:w-1/4 w-1/2  flex items-center justify-center gap-2"
                        >
                            {isLoading ? <Spinner /> : t("submit_button")}
                        </Button>
                    ) : (
                        <DialogClose asChild>
                            <Button
                                variant="outline"
                                className="!border-main_color text-white hover:bg-main_color hover:text-white md:w-1/4 w-1/2 flex items-center justify-center"
                            >
                                {t("ok")}
                            </Button>
                        </DialogClose>
                    )}
                </div>
                {/* </CardContent> */}


                {/* </Card> */}
                </div>
            </div>
        </>
    );
}
