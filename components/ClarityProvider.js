'use client';

import { useEffect } from 'react';
import Clarity from '@microsoft/clarity';

export default function ClarityProvider({ children }) {
  useEffect(() => {
    if (typeof window !== 'undefined' && "rjw2ruxjoa") {
      Clarity.init("rjw2ruxjoa");
      
      // Optional: Upgrade sessions that visit your new feature
      Clarity.upgrade('new-feature-tracking');
    }
  }, []);

  return children;
}