"use client";
import Image from "next/image";
import React from "react";
import { circle } from "@/utils/svg";
import Button from "./Button";
import { useTranslations } from "next-intl";

interface JoinProps {
  isRTL: boolean;
}

const Join: React.FC<JoinProps> = ({ isRTL }) => {
  const guide = useTranslations("guide_section");
  const joinUs = useTranslations("joinUs_section");

  return (
    <main className="container flex flex-col gap-28 max-md:px-2">
      <div className="join-container">
        <div
          className={`relative flex flex-col justify-between gap-5 w-full md:text-${
            isRTL ? "right" : "left"
          }`}
        >
          <h2
            className={`text-4xl max-md:text-3xl max-md:w-5/6 w-7/12
             font-bold text-${
               isRTL ? "right" : "left"
             } text-text_color mb-4 max-md:mx-auto max-md:text-center`}
          >
            {guide("section_title")
              .split(" ")
              .map((word, index) => (
                <span
                  key={index}
                  className={
                    ["right", "المناسب"].includes(word.toLowerCase())
                      ? "text-main_color"
                      : ""
                  }
                >
                  {word}{" "}
                </span>
              ))}
          </h2>
          <p
            className={`max-md:text-center max-md:w-5/6 max-lg:w-full w-7/12 text-text_color font-normal text-[1rem] text-${
              isRTL ? "right" : "left"
            } max-md:text-sm mb-12 max-md:mx-auto`}
          >
            {guide("section_desc")}
          </p>
          <a
            className="flex justify-start"
            target="_blank"
            rel="noopener noreferrer"
            href="https://docs.google.com/forms/d/e/1FAIpQLSf9ZbRP3Larh_q2WD6YPyxU7bUsFrjwbR43y07YM65-RIKmTA/viewform?pli=1&pli=1&fbzx=-8902506790135236095"
          >
            <Button title={guide("section_button_title")} />
          </a>
        </div>
        <div className="relative w-full md:w-1/2 h-56 md:h-80 max-md:mx-auto">
          <Image
            src={guide("section_image")}
            alt={"guide section"}
            width={500}
            height={500}
            loading="lazy"
            priority={false}
            className="rounded-lg max-md:mx-auto w-full h-full "
          />
          <div
         
            className={`absolute bottom-0 ${
              isRTL ? "left-0" : "-right-0"
            } -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-blue`}
          />
        </div>
      </div>

      <div id="join">
      </div>

      <div className="lg:grid lg:grid-cols-12 flex flex-col gap-10">
        <div className="relative w-full h-56 col-span-4 md:h-80 max-md:mx-auto">
          <Image
            src={joinUs("section_image")}
            alt={"join section"}
            layout="fill"
            objectFit="contain"
            className="rounded-lg"
            loading="lazy"
            priority={false}
          />
          <div
            className={`absolute -bottom-28 text-${
              isRTL ? "right-0" : "-left-10"
            } -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-green`}
          />
        </div>
        <div className="col-span-3"></div>
        <div
          className={`relative flex flex-col justify-between col-span-5  gap-5 w-full md:text-${
            isRTL ? "right" : "left"
          }`}
        >
          <div>
            <h2
              className={`text-4xl max-md:text-3xl max-md:w-6/6 text-${
                isRTL ? "right" : "left"
              } lg:w-${
                isRTL ? "full" : "7/12"
              } font-bold text-text_color mb-4 max-md:mx-auto max-md:text-center`}
            >
              {joinUs("section_title")
                .split(" ")
                .map((word, index) => (
                  <span
                    key={index}
                    className={
                      ["join", "انضم"].includes(word.toLowerCase())
                        ? "text-main_color"
                        : ""
                    }
                  >
                    {word}{" "}
                  </span>
                ))}
            </h2>
            <p
              className={`max-md:w-5/6 w-11/12 text-text_color font-normal text-[1rem] text-${
                isRTL ? "right" : "left"
              } max-md:text-[0.8rem] max-md:mx-auto max-md:text-center`}
            >
              {joinUs("section_desc")}
            </p>
          </div>

          <div className="max-md:mx-auto ">
            <h3 className="text-[2rem] max-md:text-center max-md:text-2xl max-md:w-5/6 w-full font-bold text-text_color mb-4 max-md:mx-auto">
              {joinUs("section_sup_title")}
            </h3>
            {[1, 2, 3].map((index) => (
              <div
                key={index}
                className={`flex h-auto leading-8 max-md:text-${
                  isRTL ? "right" : "left"
                } max-md:mx-auto w-10/12 mb-4`}
              >
                <div className="flex items-start gap-2">
                  <div className="w-4 h-4 mt-2 mx-2">{circle}</div>
                  <p className="font-normal text-[1rem] text-text_color max-md:text-sm">
                    <span className="font-bold text-[1rem] text-text_color">
                      {joinUs(`section_items.${index}.title`)}{" "}
                    </span>
                    {joinUs(`section_items.${index}.desc`)}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <a
            target="_blank"
            rel="noopener noreferrer"
            href="https://docs.google.com/forms/d/e/1FAIpQLSf9ZbRP3Larh_q2WD6YPyxU7bUsFrjwbR43y07YM65-RIKmTA/viewform?pli=1&pli=1&fbzx=-8902506790135236095"
            className="max-md:mx-auto flex max-md:justify-center"
          >
            <Button title={joinUs("section_button_title")} />
          </a>
        </div>
      </div>
    </main>
  );
};

export default Join;
