import React, { <PERSON> } from "react";
import { useTranslations } from "next-intl";
import ContinuousLoop<PERSON>ontainer from "./TechsContinuousLoop";
import { technologies } from "@/utils/constant";
import { CheckCircle, Users, Target, Lightbulb } from "lucide-react";

interface AboutProps {
  isRTL: boolean;
}

const About: FC<AboutProps> = ({ isRTL }) => {
  const t = useTranslations("about");

  return (
    <main id="about" className="min-h-screen bg-gradient-to-br from-[#0b1119] via-[#0f1419] to-[#0b1119]">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="font-bold text-5xl md:text-6xl mb-6  bg-main_color  bg-clip-text text-transparent">
            <span>{t("about.title")}</span>
            {isRTL ? "" : " P8Y"}
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 leading-relaxed mb-8">
            {t("about.aboutDescription")}
          </p>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h2 className="font-bold text-4xl md:text-5xl mb-6 text-white">
            <span className="text-main_color">{t("Why.title")}</span>
            Prohelpify {isRTL ? "؟" : "?"}
          </h2>
        </div>

        {/* Key Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {[1, 2, 3].map((index) => (
            <div
              key={index}
              className="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-main_color/50 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-main_color/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="relative z-10">
                <div className="w-16 h-16  bg-main_color  rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  {index === 1 && <Users className="w-8 h-8 text-white" />}
                  {index === 2 && <Target className="w-8 h-8 text-white" />}
                  {index === 3 && <Lightbulb className="w-8 h-8 text-white" />}
                </div>

                <h3 className="font-bold text-xl text-white mb-4 group-hover:text-main_color transition-colors duration-300">
                  {t(`Why.${index}.title`)}
                </h3>

                <p className="text-gray-300 leading-relaxed">
                  {t(`Why.${index}.content`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Mission Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-main_color/10 to-blue-400/10 backdrop-blur-sm border border-main_color/20 rounded-3xl p-8 md:p-12">
            <div className="flex items-center justify-center mb-8">
              <div className="w-20 h-20  bg-main_color  rounded-full flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-white" />
              </div>
            </div>

            <h3 className="font-bold text-3xl md:text-4xl text-center text-main_color mb-8">
              {t("Why.missionTitle")}
            </h3>

            <p className="text-lg md:text-xl text-gray-300 leading-relaxed text-center">
              {t("Why.missionDescription")}
            </p>
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h3 className="font-bold text-3xl md:text-4xl text-white mb-4">
            {t("technologiesTitle")}
          </h3>
          <p className="text-gray-300 text-lg">
            {t("technologiesDescription")}
          </p>
        </div>

        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-gray-800/30 to-gray-900/30 backdrop-blur-sm border border-gray-700/50 p-8">
          <ContinuousLoopContainer items={technologies} isRTL={isRTL} />
        </div>
      </section>

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "About Prohelpify - Your Guide to Becoming a Senior Programmer",
            description:
              "Learn coding, debugging, and best practices to advance your programming career with Prohelpify.",
            url: "https://www.example.com/about",
            image: "https://www.example.com/og-image.jpg",
          }),
        }}
      />
    </main>
  );
};

export default About;
