import React, { FC, useState } from "react";
import { useTranslations } from "next-intl";
import { ArrowR<PERSON>, ArrowLeft } from "lucide-react";
import { Button } from "./ui/button";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import MentorWizard from "./SmartForm";

interface ServicesProps {
  isRTL: boolean;
}

interface Service {
  title: string;
  description: string;
}

const Services: FC<ServicesProps> = ({ isRTL }) => {
  const t = useTranslations("services_section");
  const wizard = useTranslations("wizard");

  // Get services array from translations
  const services: Service[] = t.raw("services") || [];

  // Images for each service (from practices section)
  const serviceImages = [
    "/assists/avatars/practice-2.svg", // Session Preparation → Interview Preparation
    "/assists/avatars/practice-4.svg", // Professional Approach → Career Consultation
    "/assists/avatars/practice-1.svg", // Online Connectivity → Learning Roadmap
  ];

  // Mapping service index to wizard reason
  const serviceToReason = ["interview", "consultation", "roadmap"];

  // State for dialog and selected service
  const [selectedService, setSelectedService] = useState<number | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleServiceClick = (serviceIndex: number) => {
    setSelectedService(serviceIndex);
    setIsDialogOpen(true);
  };

  return (
    <main id="services" className="relative container px-3 max-lg:px-4 max-sm:px-3 section-padding">
      <div className="text-center mb-20">
        <h2 className="font-bold text-2xl md:text-4xl text-center mb-6">
          <span className="text-main_color">{isRTL ? "خدماتنا" : "Our"}</span>
          {isRTL ? "" : " Services"}
        </h2>
        <p className="font-normal text-text_color text-lg leading-8 max-w-4xl mx-auto">
          {isRTL
            ? "اكتشف كيف يمكننا مساعدتك في النمو في مسيرتك التقنية من خلال إرشاداتنا الخبيرة ونهجنا الشخصي."
            : "Discover how we can help you grow in your tech career with our expert guidance and personalized approach."
          }
        </p>
      </div>

      {/* Services Grid */}
      <div className="grid md:grid-cols-3 gap-8">
        {services.map((service, index) => {
          const serviceImage = serviceImages[index] || serviceImages[0];

          return (
            <div
              key={index}
              onClick={() => handleServiceClick(index)}
              className="group relative bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-main_color/50 transition-all duration-300 hover:transform hover:scale-105 cursor-pointer  "
            >
              <div className="absolute inset-0 bg-gradient-to-br from-main_color/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="relative z-10">
                <div className="w-32 h-32 bg-main_color/20 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 p-4 mx-auto">
                  <Image
                    src={serviceImage}
                    alt={service.title}
                    width={120}
                    height={120}
                    className="w-full h-full object-contain"
                    priority={index === 0}
                  />
                </div>

                <h3 className="font-bold text-xl text-white mb-4 group-hover:text-main_color transition-colors duration-300 text-center">
                  {service.title}
                </h3>

                <p className="text-gray-300 leading-relaxed mb-6 text-center">
                  {service.description}
                </p>

                <Button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent card click when button is clicked
                    handleServiceClick(index);
                  }}
                  className="w-full bg-main_color hover:bg-main_color/90 text-white font-medium flex items-center justify-center gap-2 group transition-all duration-200 md:hidden"
                >
                  <span>{isRTL ? "ابدأ الآن" : "Get Started"}</span>
                  {isRTL ? (
                    <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                  ) : (
                    <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                  )}
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Dialog for Wizard */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="md:text-4xl text-xl font-bold md:mb-6 mb-0 mt-5 text-center text-white">
              {wizard("title_1")} <span className="text-main_color">{wizard("title_2")}</span> {wizard("title_3")}
            </DialogTitle>
          </DialogHeader>
          <MentorWizard
            initialReason={selectedService !== null ? serviceToReason[selectedService] : undefined}
          />
        </DialogContent>
      </Dialog>
    </main>
  );
};

export default Services;