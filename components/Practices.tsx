import { useTranslations } from "next-intl";
import Image from "next/image";
import React, { useEffect } from "react";

interface PracticesProps {
  isRTL: boolean;
}

const Practices: React.FC<PracticesProps> = ({ isRTL }) => {
  const t = useTranslations("practices");

  const practiceItems: number[] = Array.from({ length: 7 }, (_, i) => i + 1);

  useEffect(() => {
    const script = document.createElement("script");
    script.type = "application/ld+json";
    script.textContent = JSON.stringify({
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Best Practices for Senior Programmers - Prohelpify",
      "description": "Learn coding, debugging, and best practices to advance your programming career with Prohelpify.",
      "url": "https://www.example.com/practices",
      "image": "https://www.example.com/og-image.jpg",
    });
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return (
    <main
      id="practices"
      dir="auto"
      className="mt-24 section-padding"
    >
    <div className="relative container max-sm:p-3 flex flex-col gap-20 w-full h-full mx-auto">
      <h2
        className="text-text_color font-bold text-[2.25rem] text-center"
        aria-label={t("title")}
      >
        {t("title")}
      </h2>

      <div
        className={`absolute -top-40 ${isRTL ? "-left-20" : "-right-40"} -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-green`}
      />
      <div
        className={`absolute bottom-0 ${isRTL ? "-right-40" : "-left-20"} -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-purple`}
      />

      <div className="grid grid-cols-2 gap-9 max-lg:flex max-lg:flex-col items-center justify-center w-full mx-auto">
        {practiceItems.map((id) => {
          const imgSrc = t(`items.${id}.img`, { defaultValue: "" });
          const title = t(`items.${id}.title`, { defaultValue: "Practice Title" });
          const desc = t(`items.${id}.desc`, { defaultValue: "Practice description." });

          return (
            <div key={id} className="practis-item px-5">
              <div className="bg-[#2D3C4E] w-[40%] max-sm:w-full h-full rounded-xl my-auto flex justify-center">
                {imgSrc ? (
                  <Image
                    src={imgSrc}
                    alt={`Best practice: ${title}`}
                    width={300}
                    height={300}
                    className="h-[250px] w-[300px] object-fit max-sm:mx-auto my-auto"
                    loading="lazy"
                  />
                ) : (
                  <div className="text-center text-gray-400 h-[300px] w-[300px] flex items-center justify-center">Image not available</div>
                )}
              </div>

              <div className="flex flex-col max-sm:text-center w-[60%] max-sm:w-full h-auto">
                <h3 className="text-main_color font-bold max-md:text-xl text-2xl py-2">
                  {title}
                </h3>
                <p className="text-text_color font-normal max-sm:text-sm text-md">
                  {desc}
                </p>
              </div>
            </div>
          );
        })}
      </div>
      </div>
    </main>
  );
};

export default Practices;
