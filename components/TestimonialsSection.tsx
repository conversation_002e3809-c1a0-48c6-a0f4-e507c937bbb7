import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import { useLocale, useTranslations } from "next-intl";

interface Testimonial {
  name: string;
  gender: "boy" | "girl";
  mentor: string;
  skill: string;
  content: string;
  isRTL:boolean;
}

export function TestimonialsSection() {
  const locale = useLocale();
  const isRTL = locale === "ar"; 
  const t = useTranslations("testimonials");
  const mentor: string = t("mentor");
  const topic: string = t("topic");
  const testimonials: Testimonial[] = t.raw("array");
  
  return (
    <div className="relative w-full py-16 ">
      <div className="absolute top-0 left-0 w-full text-center mx-auto  overflow-hidden leading-none">
        <svg 
          className="relative block w-full h-12 text-gray-800" 
          data-name="Layer 1" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none"
        >
          <path 
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" 
            className="fill-current"
          ></path>
        </svg>
      </div>
      
      <div className="w-[80%] text-center flex flex-col items-center mx-auto mb-12">
        <h2 className={`text-4xl font-bold text-center ${isRTL ? 'text-right' : 'text-left'} mb-4`}>
          {t("title")}
        </h2>
        <p className={`text-lg max-md:mx-auto text-gray-600 text-center `}>
          {t("subtitle")}
        </p>
      </div>

      <div className="w-[80%] mx-auto relative z-10 shadow-lg rounded-lg max-md:p-2 p-8 ">
        <AnimatedTestimonials
          testimonials={testimonials}
          mentor={mentor}
          topic={topic}
          isRTL={isRTL}
        />
      </div>

      <div className="absolute bottom-0 left-0 w-full overflow-hidden leading-none rotate-180">
        <svg 
          className="relative block w-full h-12 text-gray-800" 
          data-name="Layer 1" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none"
        >
          <path 
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" 
            className="fill-current"
          ></path>
        </svg>
      </div>
    </div>
  );
} 