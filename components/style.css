.toggle-button-cover {
  display: table-cell;
  position: relative;
  width: 200px;
  height: 140px;
  box-sizing: border-box;
}

.button-cover {
  height: 100px;
  margin: 20px;
  background-color: #fff;
  box-shadow: 0 10px 20px -8px #c5d6d6;
  border-radius: 4px;
}

.button-cover:before {
  counter-increment: button-counter;
  content: counter(button-counter);
  position: absolute;
  right: 0;
  bottom: 0;
  color: #10C6B9;
  font-size: 12px;
  line-height: 1;
  padding: 5px;
}

.button-cover,
.knobs,
.layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.button {
  position: relative;
  top: 50%;
  width: 74px;
  height: 36px;
  margin: -20px auto 0 auto;
  overflow: hidden;
}

.checkbox {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}

.knobs {
  z-index: 2;
}

.layer {
  width: 100%;
  background-color: trasparent; 
  border: 2px solid #10C6B9;
  transition: 0.3s ease all;
  z-index: 1;
}

.button.r,
.button.r .layer {
  border-radius: 100px;
}

#button-3 .knobs:before {
  content: "eng";
  position: absolute;
  top: 4px;
  left: 4px;
  width: 1.8rem;
  height: 1.8rem;
  /* color: #fff; */
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  line-height: 1;
  padding: 9px 4px;
  background-color: #10C6B9;
  border-radius: 50%;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}

#button-3 .checkbox:active + .knobs:before {
  width: 46px;
  border-radius: 100px;
}

#button-3 .checkbox:checked:active + .knobs:before {
  margin-left: -26px;
}

#button-3 .checkbox:checked + .knobs:before {
  content: "ar";
  left: 42px;
  background-color: #10C6B9;
}

#button-3 .checkbox:checked ~ .layer {
  background-color: trasparent;
}

/* Hamburger Menu Styling */
.hamburger-icon {
  display: flex;
  flex-direction: column;
  width: 30px;
  cursor: pointer;
}

.hamburger-icon .bar {
  height: 3px;
  width: 100%;
  background-color: #10C6B9;
  margin: 3px 0;
  transition: 0.4s;
}

.hamburger-icon.open .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger-icon.open .bar:nth-child(2) {
  opacity: 0;
}

.hamburger-icon.open .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .Nav {
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
}

/* Section padding for fixed navbar */
.section-padding {
  padding-top: 5rem; /* Default padding for desktop */
}

@media (max-width: 768px) {
  .section-padding {
    padding-top: 4rem; /* Smaller padding for mobile */
  }
}