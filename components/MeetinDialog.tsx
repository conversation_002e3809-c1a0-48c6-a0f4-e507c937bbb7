"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useCookie } from "@/components/context/CookieContext";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";
import { useRouter, usePathname } from "next/navigation";

interface MeetinDialogProps {
  id?:string;
  booking: string;
  name: string;
  title: string;
  isRtl: boolean;
  onConfirm?: () => void; 
}

const MeetinDialog: React.FC<MeetinDialogProps> = ({
  booking,
  name,
  title,
  isRtl,
  onConfirm,
}) => {
  const { dialogOpened, updateDialogOpened } = useCookie();
  const [open, setOpen] = useState(false);
  const [check, setCheck] = useState(false);
  const [currentLocale, setCurrentLocale] = useState("en");
  const t = useTranslations("dialog");
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const locale = window.location.pathname.split("/")[1] || "en";
    setCurrentLocale(locale);
  }, []);

  useEffect(() => {
    setOpen(dialogOpened);
  }, [dialogOpened]);

  const handleConfirm = () => {
    if (check) {
      updateDialogOpened(true);
      onConfirm?.(); 
    }
  };

  const handleCheckboxChange = () => {
    setCheck((prev) => !prev);
  };

  const handlePracticesClick = async () => {
    setOpen(false);
    handleConfirm();

    const isHomePage = pathname === `/${currentLocale}` || pathname === "/";
    
    if (!isHomePage) {
      // Navigate to home page
      await router.push(`/${currentLocale}`);
      
      // Wait for navigation and DOM to be ready
      setTimeout(() => {
        const practicesSection = document.getElementById('practices');
        if (practicesSection) {
          practicesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 500);
    } else {
      // Already on home page, just scroll
      const practicesSection = document.getElementById('practices');
      if (practicesSection) {
        practicesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  return (
    <>
      {dialogOpened ? (
        <a
          href={booking}
          target="_blank"
          rel="noopener noreferrer"
          className="ml-3"
          aria-label={`Book a session with ${name}`}
        >
          {title}
        </a>
      ) : (
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger onClick={() => setOpen(true)}>{title}</DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <div className="py-5">
                <DialogTitle
                  className={isRtl ? "text-right pr-3" : "text-left pl-3"}
                >
                  {t("title")}
                </DialogTitle>
                <DialogDescription
                  className={`!text-slate-50 pt-4 ${
                    isRtl ? "text-right pr-3" : "text-left pl-3"
                  }`}
                >
                  {t("description")}
                </DialogDescription>
              </div>
            </DialogHeader>
            <div className="flex flex-col-reverse justify-between px-3 items-start gap-4">
              <div className="flex items-center gap-5">
                <a
                  href={booking}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={handleConfirm} 
                  aria-label={`Book a session with ${name}`}
                >
                  <DialogClose>{t("confirm")}</DialogClose>
                </a>
                <button 
                  onClick={handlePracticesClick}
                  className="!bg-transparent !p-0 hover:underline"
                >
                  {t("goPractises")}
                </button>
              </div>
              <div className="flex gap-2 items-center">
                <Checkbox
                  id="dialog"
                  onClick={handleCheckboxChange}
                  className="p-0 bg-transparent"
                />
                <Label htmlFor="dialog" className="leading-4">
                  {t("checkbox")}
                </Label>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default MeetinDialog;