import { useState, useEffect, useRef } from 'react';
import { useTranslations } from "next-intl";

interface Sparkle {
  id: number;
  top: number;
  left: number;
  duration: number;
}

interface Pulse {
  id: number;
}

export default function AnimatedButtonPreview() {
  const wizard = useTranslations("wizard");
  const buttonContainerRef = useRef(null);
  const [sparkles, setSparkles] = useState<Sparkle[]>([]);
  const [pulses, setPulses] = useState<Pulse[]>([]);
  const sparkleIdRef = useRef(0);
  const pulseIdRef = useRef(0);
  
  useEffect(() => {
    const sparkleInterval = setInterval(() => {
      const newSparkle = {
        id: sparkleIdRef.current++,
        top: Math.random() * 100,
        left: Math.random() * 100,
        duration: 1 + Math.random() * 2,
      };
      
      setSparkles(prev => [...prev, newSparkle]);
      
      setTimeout(() => {
        setSparkles(prev => prev.filter(sparkle => sparkle.id !== newSparkle.id));
      }, newSparkle.duration * 1000);
    }, 300);
    
    return () => clearInterval(sparkleInterval);
  }, []);
  
  useEffect(() => {
    const pulseInterval = setInterval(() => {
      const newPulse = { id: pulseIdRef.current++ };
      setPulses(prev => [...prev, newPulse]);
      
      setTimeout(() => {
        setPulses(prev => prev.filter(pulse => pulse.id !== newPulse.id));
      }, 1500);
    }, 1500);
    
    return () => clearInterval(pulseInterval);
  }, []);
  
  return (
    <div className="flex justify-center items-center ">
      <div 
        ref={buttonContainerRef}
        className="relative cursor-pointer"
      >
        <button
          className="relative bg-main_color text-white text-xl font-bold py-2 px-6 rounded-md z-10 hover:shadow-lg"
          style={{
            animation: "attention 0.7s ease infinite, float 3s ease-in-out infinite",
          }}
        >
          <span className="relative z-10">{wizard("wizard_dialog.button")}</span>
        </button>
        
        {pulses.map(pulse => (
          <div
            key={pulse.id}
            className="absolute top-0 left-0 w-full h-full rounded-md bg-main_color opacity-40 z-0"
            style={{
              animation: "pulse 1.5s linear",
            }}
          />
        ))}
        
        <style jsx>{`
          @keyframes pulse {
            0% {
              transform: scale(1);
              opacity: 0.7;
            }
            100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }
          
          // @keyframes float {
          //   0%, 100% {
          //     transform: translateY(0);
          //   }
          //   50% {
          //     transform: translateY(-10px);
          //   }
          // }
        `}</style>
      </div>
    </div>
  );
}