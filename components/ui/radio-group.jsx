"use client"

import * as React from "react"
import * as RadioGroupPrimitive from "@radix-ui/react-radio-group"
import { Circle } from "lucide-react"

import { cn } from "@/lib/utils"

const RadioGroup = React.forwardRef(({ className, ...props }, ref) => {
  return (<RadioGroupPrimitive.Root className={cn("grid gap-2", className)} {...props} ref={ref} />);
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const RadioGroupItem = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        "aspect-square h-4 w-4 !m-0 p-[1px] rounded-full border-2 border-white bg-transparent",
        "data-[state=checked]:border-main_color data-[state=checked]:bg-transparent",
        "focus:outline-none focus-visible:ring-2 focus-visible:ring-main_color/30",
        className
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="flex p-[1px]  items-center justify-center">
        <Circle className="h-1.5 w-1.5 fill-white text-white" />
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
})
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

export { RadioGroup, RadioGroupItem }