"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";

interface FilterValue {
    value: string;
    label: string;
}

interface ComboboxDemoProps {
    filterName: string;
    filterValues: FilterValue[];
    multi?: boolean;
    onValueChange?: (value: string[] | string) => void;
}

export function ComboboxDemo({
    filterName,
    filterValues,
    multi = false,
    onValueChange,
}: ComboboxDemoProps) {
    const [open, setOpen] = React.useState<boolean>(false);
    const [selectedValues, setSelectedValues] = React.useState<string[]>([]); // for multi-select
    const [singleValue, setSingleValue] = React.useState<string>(""); // for single-select

    const toggleValue = (val: string) => {
        if (multi) {
            let updatedValues: string[];
            if (selectedValues.includes(val)) {
                updatedValues = selectedValues.filter((v) => v !== val);
            } else {
                updatedValues = [...selectedValues, val];
            }
            setSelectedValues(updatedValues);
            onValueChange?.(updatedValues);
        } else {
            const finalValue = val === singleValue ? "" : val;
            setSingleValue(finalValue);
            onValueChange?.(finalValue);
            setOpen(false);
        }
    };

    const displayLabel = () => {
        if (multi) {
            if (selectedValues.length === 0) return filterName;
            return selectedValues
                .map((v) => filterValues.find((item) => item.value === v)?.label)
                .filter(Boolean)
                .join(", ");
        } else {
            return singleValue
                ? filterValues.find((item) => item.value === singleValue)?.label
                : filterName;
        }
    };

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-[200px] justify-between"
                >
                    {displayLabel()}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0">
                <Command>
                    <CommandInput placeholder={`Search ${filterName}...`} className="h-9" />
                    <CommandList>
                        <CommandEmpty>No options found.</CommandEmpty>
                        <CommandGroup>
                            {filterValues.map((item) => {
                                const isSelected = multi
                                    ? selectedValues.includes(item.value)
                                    : singleValue === item.value;

                                return (
                                    <CommandItem
                                        key={item.value}
                                        value={item.value}
                                        onSelect={() => toggleValue(item.value)}
                                    >
                                        {item.label}
                                        <Check
                                            className={cn(
                                                "ml-auto h-4 w-4",
                                                isSelected ? "opacity-100" : "opacity-0"
                                            )}
                                        />
                                    </CommandItem>
                                );
                            })}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
}
