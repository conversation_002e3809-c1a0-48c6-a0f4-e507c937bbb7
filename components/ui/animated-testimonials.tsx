"use client";

import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useCallback, useEffect, useState } from "react";

interface Testimonial {
  name: string;
  gender: "boy" | "girl";
  mentor: string;
  skill: string;
  content: string;
}

interface AnimatedTestimonialsProps {
  testimonials: Testimonial[];
  isRTL?: boolean;
  mentor: string;
  topic: string;
  autoplay?: boolean;
}

export const AnimatedTestimonials: React.FC<AnimatedTestimonialsProps> = ({
  testimonials,
  isRTL = false,
  mentor,
  topic,
  autoplay = true,
}) => {
  //https://avatar.iran.liara.run/public/boy?username=${username}
  const [active, setActive] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleNext = useCallback((): void => {
    setActive((prev) => (prev + 1) % testimonials.length);
  }, [testimonials.length]);
  
  useEffect(() => {
    if (autoplay) {
      const interval = setInterval(handleNext, 4000);
      return () => clearInterval(interval);
    }
  }, [autoplay, handleNext]);

  const isActive = (index: number): boolean => index === active;

  if (testimonials.length === 0) return null;

  return (
    <div className=" md:max-w-[70%] lg:max-w-[50%] mx-auto flex justify-center items-center antialiased font-normal px-4  md:px-8 lg:px-12 pt-10 ">
      <div className="relative grid grid-cols-1 w-fit sm:grid-cols-[180px_250px] md:grid-cols-[180px_350px]   sm:gap-20 gap-10 mx-auto ">
        <div>
          <div className="relative h-[200px] md:w-full w-[180px] m-auto">
            {isMounted && (
              <AnimatePresence>
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={`${testimonial.name}-${index}`}
                    initial={{
                      opacity: 0,
                      scale: 0.9,
                      z: -100,
                    }}
                    animate={{
                      opacity: isActive(index) ? 1 : 0.7,
                      scale: isActive(index) ? 1 : 0.95,
                      z: isActive(index) ? 0 : -100,
                      zIndex: isActive(index)
                        ? 50
                        : testimonials.length + 2 - index,
                      y: isActive(index) ? [0, -80, 0] : 0,
                    }}
                    exit={{
                      opacity: 0,
                      scale: 0.9,
                      z: 100,
                    }}
                    transition={{
                      duration: 0.4,
                      ease: "easeInOut",
                    }}
                    className="absolute  origin-center"
                  >
                    <Image
                      src={`https://avatar.iran.liara.run/public/${testimonial.gender}?username=${testimonial.name}`}
                      alt={testimonial.name}
                      width={180}
                      height={200}
                      draggable={false}
                      loading="lazy"
                      priority={false}
                      className=" object-cover object-center"
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            )}
          </div>
        </div>
        <div className="flex justify-between px-5 lg:w-[450px] md:h-[300px] h-[450px] sm:w-[280px] md:w-[450px] w-[280px] flex-col pb-4">
          <motion.div
            key={active}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeInOut" }}
          >
            <h3 className="text-2xl font-bold text-white ">
              {testimonials[active].name}
            </h3>
            <p className="text-sm text-neutral-400">
              <span className="text-neutral-300 font-semibold">{mentor} </span>
              {testimonials[active].mentor}
            </p>
            <p className="text-sm text-neutral-400">
              <span className="text-neutral-300 font-semibold">{topic} </span>
              {testimonials[active].skill}
            </p>
            <motion.p className="text-lg text-gray-300 my-8 dark:text-neutral-300 text-wrap">
              {testimonials[active].content.split(" ").map((word, index) => (
                <motion.span
                  key={index}
                  initial={{ filter: "blur(10px)", opacity: 0, y: 5 }}
                  animate={{ filter: "blur(0px)", opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.2,
                    ease: "easeInOut",
                    delay: 0.02 * index,
                  }}
                  className="inline-block"
                >
                  {word}&nbsp;
                </motion.span>
              ))}
            </motion.p>
          </motion.div>
          <div
            className={`flex gap-4 ${!isRTL && "flex-row-reverse justify-end"}`}
          ></div>
        </div>
      </div>
    </div>
  );
};
