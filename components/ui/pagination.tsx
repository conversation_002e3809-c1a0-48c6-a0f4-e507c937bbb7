import * as React from "react";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

type PaginationProps = React.HTMLAttributes<HTMLElement>;

const Pagination: React.FC<PaginationProps> = ({ className, ...props }) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
);
Pagination.displayName = "Pagination";

type PaginationContentProps = React.HTMLAttributes<HTMLUListElement>;

const PaginationContent = React.forwardRef<HTMLUListElement, PaginationContentProps>(
  ({ className, ...props }, ref) => (
    <ul ref={ref} className={cn("flex flex-row items-center gap-1", className)} {...props} />
  )
);
PaginationContent.displayName = "PaginationContent";

type PaginationItemProps = React.HTMLAttributes<HTMLLIElement>;

const PaginationItem = React.forwardRef<HTMLLIElement, PaginationItemProps>(
  ({ className, ...props }, ref) => (
    <li ref={ref} className={cn("", className)} {...props} />
  )
);
PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = React.AnchorHTMLAttributes<HTMLAnchorElement> & {
  isActive?: boolean;
  size?: "default" | "sm" | "lg" | "icon";
};

const PaginationLink: React.FC<PaginationLinkProps> = ({
  className,
  isActive,
  size = "icon",
  ...props
}) => (
  <a
    aria-current={isActive ? "page" : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? "outline" : "ghost",
        size,
      }),
      className
    )}
    {...props}
  />
);
PaginationLink.displayName = "PaginationLink";
const PaginationPrevious: React.FC<
  React.AnchorHTMLAttributes<HTMLAnchorElement> & { lang?: "en" | "ar"; isRTL?: boolean }
> = ({ className, lang = "en", isRTL = false, ...props }) => (
  <PaginationLink
    aria-label={lang === "ar" ? "الصفحة السابقة" : "Go to previous page"}
    size="default"
    className={cn("gap-1 pl-2.5", className)}
    {...props}
  >
    {isRTL ? <ChevronRight className="h-4 w-4"/> : <ChevronLeft className="h-4 w-4"/>}
    <span>{lang === "ar" ? "السابق" : "Previous"}</span>
  </PaginationLink>
);
PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext: React.FC<
  React.AnchorHTMLAttributes<HTMLAnchorElement> & { lang?: "en" | "ar"; isRTL?: boolean }
> = ({ className, lang = "en", isRTL = false, ...props }) => (
  <PaginationLink
    aria-label={lang === "ar" ? "الصفحة التالية" : "Go to next page"}
    size="default"
    className={cn("gap-1 pr-2.5", className)}
    {...props}
  >
    <span>{lang === "ar" ? "التالي" : "Next"}</span>
    {isRTL ? <ChevronLeft className="h-4 w-4"/> : <ChevronRight className="h-4 w-4"/>}
  </PaginationLink>
);
PaginationNext.displayName = "PaginationNext";


const PaginationEllipsis: React.FC<React.HTMLAttributes<HTMLSpanElement>> = ({
  className,
  ...props
}) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
);
PaginationEllipsis.displayName = "PaginationEllipsis";

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
};
