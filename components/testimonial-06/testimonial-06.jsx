"use client";

import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";
import { StarIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { useTranslations } from "next-intl";



const Testimonial06 = ({ isRTl }) => {
  const [api, setApi] = useState();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const t = useTranslations("testimonials");
  const mentor = t("mentor");
  const topic = t("topic");
  const testimonials = t.raw("array");

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <div className="  flex   justify-center items-center py-12 px-6">
      <div className="w-full">
        <h2 className="mb-14 text-5xl md:text-6xl font-bold text-center tracking-tight">
          Testimonials
        </h2>
        <div className=" w-full lg:max-w-screen-lg xl:max-w-screen-xl mx-auto md:px-12">
          <Carousel setApi={setApi}>
            <CarouselContent isrtl={isRTl}>

              {testimonials.map((testimonial) => (
                <CarouselItem key={testimonial.id} className="mb-5">
                    <TestimonialCard testimonial={testimonial} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious />
            <CarouselNext />
          </Carousel>
          <div className="flex items-center  w-fit mx-auto justify-center gap-2">
            {Array.from({ length: count }).map((_, index) => (
              <button
                key={index}
                onClick={() => api?.scrollTo(index)}
                className={cn(
                  " rounded-full !p-1 md:!p-2 border-2 bg-transparent",
                  {
                    "bg-main_color  border-main_color": current === index + 1,
                  }
                )}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const TestimonialCard = ({ testimonial, isRTl }) => (
  <div className="mb-8  w-full    bg-[#1D2B3B] flex flex-col justify-between rounded-xl py-8 px-6 sm:py-6">
    <div className="flex items-center justify-between gap-20">
      <div className="flex flex-col justify-between ">
        <div className="flex items-center justify-between gap-1 ">
          <div className="hidden sm:flex md:hidden items-center gap-4">
            <Avatar className="w-8 h-8 md:w-10 md:h-10">
              <AvatarFallback className="text-xl font-medium bg-primary text-primary-foreground">
                {testimonial.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="sm:text-lg text-md font-semibold">
                {testimonial.name}
              </p>
              <p className="text-sm text-gray-500">{testimonial.designation}</p>
            </div>
          </div>
        </div>
        <p className="mt-6 w-[80%] text-lg text-wrap sm:text-2xl lg:text-[1.75rem] xl:text-3xl leading-normal lg:!leading-normal font-semibold tracking-tight">
          &quot;{testimonial.content}&quot;
        </p>
        <div className="flex sm:hidden md:flex mt-6 items-center gap-4">
          <Avatar>
            <AvatarFallback className="text-xl font-medium bg-primary text-primary-foreground">
              {testimonial.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-lg font-semibold">{testimonial.name}</p>
            <p className="text-sm text-gray-500">{testimonial.skill}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default Testimonial06;
