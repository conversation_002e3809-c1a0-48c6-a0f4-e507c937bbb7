import { useLocale, useTranslations } from "next-intl";
import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "./ui/accordion";
import Link from "next/link";
import { Button } from "./ui/button";

interface FAQSectionProps {
  isRTL: boolean;
}

const FAQSection: React.FC<FAQSectionProps> = ({ isRTL }) => {
  const locale = useLocale();
  const faq = useTranslations("faq");
  const textDirection = isRTL ? "text-right" : "text-left";

  return (
    <div className="conatiner flex flex-col items-center gap-20 mb-20 w-full section-padding">
      <h2
        className="text-text_color font-bold text-[2.25rem] text-center"
        aria-label="Best Practices for Senior Programmers"
      >
        {faq("title")}
      </h2>

      <div className="flex flex-wrap w-[100%] justify-center">
        <Accordion
          type="single"
          collapsible
          className="w-[80%] flex flex-col md:flex-row flex-wrap gap-1 bg-transparent"
        >
          {[1, 2, 3, 4].map((index) => (
            <AccordionItem value={index.toString()} key={index} className="w-full">
              <AccordionTrigger
                className={`md:text-xl text-md hover:no-underline hover:text-slate-300 font-semibold ${textDirection}`}
              >
                {faq(`${index}.q`)}
              </AccordionTrigger>
              <AccordionContent className="md:px-12 p-3 md:text-lg text-sm">
                {faq(`${index}.a`)}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      <Link href={`/${locale}/faq`} passHref>
        <Button className="bg-main_color text-lg font-bold !px-8 py-5 hover:bg-main_color">
          {faq("button")}
        </Button>
      </Link>
    </div>
  );
};

export default FAQSection;
