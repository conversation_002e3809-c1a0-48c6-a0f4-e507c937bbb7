import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/lib/type";
import { useLocale } from "next-intl";

export const MentorCard = ({ mentor }: { mentor: <PERSON><PERSON> }) => {
    const locale = useLocale();
    const isRTL = locale === 'ar';
    const [imageError, setImageError] = useState(false);
    return (
      <div className="bg-[#1D2B3B] rounded-xl p-5">
        <div className="flex max-md:flex-col justify-around w-full">
          <div className="p-3 max-lg:w-full w-1/3">
            <Image
              width={100}
              height={100}
              src={imageError ? "/assists/avatars/default-image.svg" : mentor.profile_image.url}
              alt={locale === 'ar' ? mentor.name_ar : mentor.name_en}
              className="w-full h-auto max-sm:w-full mx-auto rounded-md object-cover"
              onError={() => setImageError(true)}
              priority={false}
              loading="lazy"
            />
          </div>

          <div className="max-lg:w-full w-2/3">
            <div className="flex justify-between">
              <h3 className="text-xl font-bold p-2 w-full">
                {locale === 'ar' ? mentor.name_ar : mentor.name_en}
              </h3>
              <div className="bg-[#EDF8FF] rounded-xl px-2 py-1 my-auto">
                <p className="text-[0.75rem] text-[#2754B8] font-medium w-max">
                  {mentor.current_company?.name || "freelance"}
                </p>
              </div>
            </div>

            <div className="flex">
              <p className="text-sm p-2 h-fit my-auto">{mentor.level}</p>
            </div>

            <div className="py-1 flex w-full">
              <ul className="flex flex-wrap">
                {mentor.skills.map(skill => (
                  <li
                    key={skill._id}
                    className="px-2 p-1 m-1 w-fit rounded-full text-xs font-medium bg-[#3C4E63]"
                  >
                    {skill.title}
                  </li>
                ))}
              </ul>
            </div>

            <div className="py-1 flex w-full">
              <ul className="flex flex-wrap">
                {mentor.areas_of_expertise.map((exp, index) => (
                  <li
                    key={index}
                    className="px-2 p-1 m-1 w-fit rounded-full text-xs font-medium bg-[#3C4E63]"
                  >
                    {locale === 'ar' && exp.title_ar ? exp.title_ar : exp.title_en}
                  </li>
                ))}
              </ul>
            </div>

            <div className="py-1 flex w-full">
              <ul className="flex flex-wrap">
                {mentor.topics.map(topic => (
                  <li
                    key={topic._id}
                    className="px-2 p-1 m-1 w-fit rounded-full text-xs font-medium bg-[#3C4E63]"
                  >
                    {topic.title}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="px-3 pt-4 mb-7 flex items-end justify-between text-[1rem] font-normal">
          <p className="flex-1 text-gray-300">
            {locale === 'ar' ? mentor.bio_ar : mentor.bio_en}
          </p>
        </div>
      </div>
    );
  };

export  const MentorCardSkeleton = () => (
    <div className="bg-[#1D2B3B] rounded-xl p-5 animate-pulse">
      <div className="flex max-md:flex-col justify-around w-full">
        <div className="p-3 max-lg:w-full w-1/3">
          <div className="w-full h-[200px] bg-gray-700 rounded-md" />
        </div>
        <div className="max-lg:w-full w-2/3">
          <div className="h-6 bg-gray-700 rounded w-3/4 mb-4" />
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-4" />
          <div className="h-4 bg-gray-700 rounded w-1/3" />
        </div>
      </div>
    </div>
  );