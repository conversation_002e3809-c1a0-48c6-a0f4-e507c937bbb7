import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useTranslations } from "next-intl";

interface FaqProps {
  locale: string;
}

const Faq: React.FC<FaqProps> = ({ locale }) => {
  const isRTL = locale === "ar";
  const textDirection = isRTL ? "text-right" : "text-left";

  const faq = useTranslations("faq");

  return (
    <div className="md:p-5">
      <h1 className={`md:text-[36px] text-[22px] pb-7 ${textDirection}`}>
        {faq("title")}
      </h1>
      <Accordion
        type="single"
        collapsible
        className="w-full flex flex-col md:flex-row flex-wrap gap-1 bg-transparent"
      >
        {[...Array(15)].map((_, index) => {
          const questionKey = `${index + 1}.q`;
          const answerKey = `${index + 1}.a`;

          return (
            <AccordionItem
              value={String(index + 1)}
              key={index + 1}
              className="md:w-[49%] w-full"
            >
              <AccordionTrigger
                className={`md:text-xl text-md hover:no-underline hover:text-slate-300 font-semibold ${textDirection}`}
              >
                {faq(questionKey)}
              </AccordionTrigger>
              <AccordionContent className="md:px-12 p-3 text-lg">
                {faq(answerKey)}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </div>
  );
};

export default Faq;
